package rt.tt.temp.managers

import android.content.Context
import rt.tt.temp.ui.viewmodels.GoogleSheetsViewModel
import rt.tt.temp.data.Product

/**
 * Singleton manager for Google Sheets operations
 * Provides global access to auto-sync functionality
 */
class GoogleSheetsManager private constructor() {
    private var viewModel: GoogleSheetsViewModel? = null
    
    companion object {
        @Volatile
        private var INSTANCE: GoogleSheetsManager? = null
        
        fun getInstance(): GoogleSheetsManager {
            return INSTANCE ?: synchronized(this) {
                val instance = GoogleSheetsManager()
                INSTANCE = instance
                instance
            }
        }
    }
    
    fun initialize(context: Context) {
        if (viewModel == null) {
            viewModel = GoogleSheetsViewModel(context.applicationContext)
        }
    }
    
    fun getViewModel(): GoogleSheetsViewModel? = viewModel
    
    // Convenience methods for database operations with auto-sync
    suspend fun insertProduct(product: Product) {
        viewModel?.insertProduct(product)
    }
    
    suspend fun updateProduct(product: Product) {
        viewModel?.updateProduct(product)
    }
    
    suspend fun deleteProduct(product: Product) {
        viewModel?.deleteProduct(product)
    }
    
    suspend fun updateProductQuantity(productId: Long, newQuantity: Int) {
        viewModel?.updateProductQuantity(productId, newQuantity)
    }
    
    fun markDataAsChanged() {
        viewModel?.markDataAsChanged()
    }
}
