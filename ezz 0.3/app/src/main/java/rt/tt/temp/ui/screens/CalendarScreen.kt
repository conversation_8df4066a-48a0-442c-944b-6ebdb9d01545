package rt.tt.temp.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.Product
import rt.tt.temp.ui.components.TranslatableText
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    database: AppDatabase,
    translationViewModel: TranslationViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var selectedDate by remember { mutableStateOf(Date()) }
    var currentMonth by remember { mutableStateOf(Calendar.getInstance()) }
    var monthKey by remember { mutableStateOf(0) } // Force recomposition trigger
    var products by remember { mutableStateOf<List<Product>>(emptyList()) }
    var selectedDateProducts by remember { mutableStateOf<List<Product>>(emptyList()) }
    var viewMode by remember { mutableStateOf(CalendarViewMode.MONTH) }
    var showProductDetails by remember { mutableStateOf(false) }
    var selectedProduct by remember { mutableStateOf<Product?>(null) }

    // Load products
    LaunchedEffect(Unit) {
        products = database.productDao().getAllProducts()
    }

    // Update selected date products when date or products change
    LaunchedEffect(selectedDate, products) {
        val calendar = Calendar.getInstance()
        calendar.time = selectedDate
        val selectedDay = calendar.get(Calendar.DAY_OF_YEAR)
        val selectedYear = calendar.get(Calendar.YEAR)

        selectedDateProducts = products.filter { product ->
            val productCalendar = Calendar.getInstance()
            productCalendar.timeInMillis = product.expireDate
            val productDay = productCalendar.get(Calendar.DAY_OF_YEAR)
            val productYear = productCalendar.get(Calendar.YEAR)

            productDay == selectedDay && productYear == selectedYear
        }.sortedBy { it.expireDate }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with title and view mode toggle
        CalendarHeader(
            currentMonth = currentMonth,
            viewMode = viewMode,
            onViewModeChange = { viewMode = it },
            onPreviousMonth = {
                val newMonth = currentMonth.clone() as Calendar
                newMonth.add(Calendar.MONTH, -1)
                currentMonth = newMonth
                monthKey++ // Trigger recomposition
                android.util.Log.d("Calendar", "Previous month: ${java.text.SimpleDateFormat("MMMM yyyy", java.util.Locale.getDefault()).format(newMonth.time)}")
            },
            onNextMonth = {
                val newMonth = currentMonth.clone() as Calendar
                newMonth.add(Calendar.MONTH, 1)
                currentMonth = newMonth
                monthKey++ // Trigger recomposition
                android.util.Log.d("Calendar", "Next month: ${java.text.SimpleDateFormat("MMMM yyyy", java.util.Locale.getDefault()).format(newMonth.time)}")
            },
            onToday = {
                currentMonth = Calendar.getInstance()
                selectedDate = Date()
                monthKey++ // Trigger recomposition
                android.util.Log.d("Calendar", "Today: ${java.text.SimpleDateFormat("MMMM yyyy", java.util.Locale.getDefault()).format(currentMonth.time)}")
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        when (viewMode) {
            CalendarViewMode.MONTH -> {
                // Month view with calendar grid
                CalendarMonthView(
                    currentMonth = currentMonth,
                    selectedDate = selectedDate,
                    products = products,
                    onDateSelected = { selectedDate = it },
                    monthKey = monthKey, // Pass monthKey for recomposition
                    modifier = Modifier.weight(1f)
                )
            }
            CalendarViewMode.AGENDA -> {
                // Agenda view with upcoming expiries
                CalendarAgendaView(
                    products = products,
                    translationViewModel = translationViewModel,
                    onProductClick = { product ->
                        selectedProduct = product
                        showProductDetails = true
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Selected date products section
        if (viewMode == CalendarViewMode.MONTH) {
            SelectedDateProductsSection(
                selectedDate = selectedDate,
                products = selectedDateProducts,
                translationViewModel = translationViewModel,
                onProductClick = { product ->
                    selectedProduct = product
                    showProductDetails = true
                }
            )
        }
    }

    // Product details dialog
    if (showProductDetails && selectedProduct != null) {
        ProductDetailsDialog(
            product = selectedProduct!!,
            translationViewModel = translationViewModel,
            onDismiss = {
                showProductDetails = false
                selectedProduct = null
            }
        )
    }
}

enum class CalendarViewMode {
    MONTH, AGENDA
}

@Composable
private fun CalendarHeader(
    currentMonth: Calendar,
    viewMode: CalendarViewMode,
    onViewModeChange: (CalendarViewMode) -> Unit,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    onToday: () -> Unit
) {
    val monthFormat = SimpleDateFormat("MMMM yyyy", Locale.getDefault())

    Column {
        // Title and view mode toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "📅 Inventory Calendar",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            // View mode toggle
            Row(
                modifier = Modifier
                    .background(
                        MaterialTheme.colorScheme.surfaceVariant,
                        RoundedCornerShape(20.dp)
                    )
                    .padding(4.dp)
            ) {
                CalendarViewMode.values().forEach { mode ->
                    val isSelected = viewMode == mode
                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(16.dp))
                            .background(
                                if (isSelected) MaterialTheme.colorScheme.primary
                                else Color.Transparent
                            )
                            .clickable { onViewModeChange(mode) }
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    ) {
                        Text(
                            text = when (mode) {
                                CalendarViewMode.MONTH -> "Month"
                                CalendarViewMode.AGENDA -> "Agenda"
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isSelected) MaterialTheme.colorScheme.onPrimary
                            else MaterialTheme.colorScheme.onSurfaceVariant,
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Month navigation (only for month view)
        if (viewMode == CalendarViewMode.MONTH) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onPreviousMonth) {
                    Icon(
                        Icons.Default.ChevronLeft,
                        contentDescription = "Previous month",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                Text(
                    text = monthFormat.format(currentMonth.time),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Row {
                    TextButton(onClick = onToday) {
                        Text(
                            text = "Today",
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    IconButton(onClick = onNextMonth) {
                        Icon(
                            Icons.Default.ChevronRight,
                            contentDescription = "Next month",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CalendarMonthView(
    currentMonth: Calendar,
    selectedDate: Date,
    products: List<Product>,
    onDateSelected: (Date) -> Unit,
    monthKey: Int, // Add monthKey parameter
    modifier: Modifier = Modifier
) {
    val daysOfWeek = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")

    // Calculate calendar days
    val calendarDays = remember(currentMonth, monthKey) {
        generateCalendarDays(currentMonth)
    }

    // Group products by date for quick lookup
    val productsByDate = remember(products) {
        products.groupBy { product ->
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = product.expireDate
            "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.DAY_OF_YEAR)}"
        }
    }

    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Days of week header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                daysOfWeek.forEach { day ->
                    Text(
                        text = day,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.weight(1f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Calendar grid
            LazyVerticalGrid(
                columns = GridCells.Fixed(7),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(calendarDays) { day ->
                    CalendarDayCell(
                        day = day,
                        isSelected = isSameDay(day.date, selectedDate),
                        isToday = isSameDay(day.date, Date()),
                        isCurrentMonth = day.isCurrentMonth,
                        productCount = if (day.isCurrentMonth) {
                            val calendar = Calendar.getInstance()
                            calendar.time = day.date
                            val key = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.DAY_OF_YEAR)}"
                            productsByDate[key]?.size ?: 0
                        } else 0,
                        hasExpiredProducts = if (day.isCurrentMonth) {
                            val calendar = Calendar.getInstance()
                            calendar.time = day.date
                            val key = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.DAY_OF_YEAR)}"
                            productsByDate[key]?.any { it.isExpired } ?: false
                        } else false,
                        onClick = { if (day.isCurrentMonth) onDateSelected(day.date) }
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarDayCell(
    day: CalendarDay,
    isSelected: Boolean,
    isToday: Boolean,
    isCurrentMonth: Boolean,
    productCount: Int,
    hasExpiredProducts: Boolean,
    onClick: () -> Unit
) {
    val calendar = Calendar.getInstance()
    calendar.time = day.date
    val dayNumber = calendar.get(Calendar.DAY_OF_MONTH)

    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .clip(RoundedCornerShape(8.dp))
            .background(
                when {
                    isSelected -> MaterialTheme.colorScheme.primary
                    isToday -> MaterialTheme.colorScheme.primaryContainer
                    !isCurrentMonth -> Color.Transparent
                    else -> MaterialTheme.colorScheme.surface
                }
            )
            .border(
                width = if (isToday && !isSelected) 2.dp else 0.dp,
                color = MaterialTheme.colorScheme.primary,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(enabled = isCurrentMonth) { onClick() }
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = dayNumber.toString(),
                style = MaterialTheme.typography.bodySmall,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isSelected -> MaterialTheme.colorScheme.onPrimary
                    isToday -> MaterialTheme.colorScheme.primary
                    !isCurrentMonth -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                    else -> MaterialTheme.colorScheme.onSurface
                }
            )

            if (isCurrentMonth && productCount > 0) {
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .clip(CircleShape)
                        .background(
                            if (hasExpiredProducts) Color.Red
                            else if (isSelected) MaterialTheme.colorScheme.onPrimary
                            else MaterialTheme.colorScheme.secondary
                        )
                )
            }
        }
    }
}

@Composable
private fun CalendarAgendaView(
    products: List<Product>,
    translationViewModel: TranslationViewModel,
    onProductClick: (Product) -> Unit,
    modifier: Modifier = Modifier
) {
    val today = Date()
    val upcomingProducts = remember(products) {
        products.filter { product ->
            product.expireDate >= today.time
        }.sortedBy { it.expireDate }
    }

    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "📋 Upcoming Expiries",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            if (upcomingProducts.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = Color(0xFF4CAF50)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "No upcoming expiries!",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(upcomingProducts) { product ->
                        AgendaProductCard(
                            product = product,
                            translationViewModel = translationViewModel,
                            onClick = { onProductClick(product) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun AgendaProductCard(
    product: Product,
    translationViewModel: TranslationViewModel,
    onClick: () -> Unit
) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    val daysUntilExpiry = ((product.expireDate - System.currentTimeMillis()) / (1000 * 60 * 60 * 24)).toInt()

    val cardColor = when {
        product.isExpired -> Color(0xFFFFEBEE)
        daysUntilExpiry <= 7 -> Color(0xFFFFF3E0)
        daysUntilExpiry <= 30 -> Color(0xFFF3E5F5)
        else -> MaterialTheme.colorScheme.surface
    }

    val textColor = when {
        product.isExpired -> Color(0xFFD32F2F)
        daysUntilExpiry <= 7 -> Color(0xFFFF8F00)
        daysUntilExpiry <= 30 -> Color(0xFF7B1FA2)
        else -> MaterialTheme.colorScheme.primary
    }

    ElevatedCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.elevatedCardColors(containerColor = cardColor),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                TranslatableText(
                    text = product.productName,
                    translationViewModel = translationViewModel,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = "${product.branchName} • Qty: ${product.currentQuantity}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = "Expires: ${dateFormat.format(Date(product.expireDate))}",
                    style = MaterialTheme.typography.bodySmall,
                    color = textColor,
                    fontWeight = FontWeight.Medium
                )
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = when {
                        product.isExpired -> "EXPIRED"
                        daysUntilExpiry == 0 -> "TODAY"
                        daysUntilExpiry == 1 -> "TOMORROW"
                        else -> "${daysUntilExpiry}d"
                    },
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold,
                    color = textColor
                )

                Icon(
                    imageVector = when {
                        product.isExpired -> Icons.Default.Error
                        daysUntilExpiry <= 7 -> Icons.Default.Warning
                        else -> Icons.Default.Schedule
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = textColor
                )
            }
        }
    }
}

@Composable
private fun SelectedDateProductsSection(
    selectedDate: Date,
    products: List<Product>,
    translationViewModel: TranslationViewModel,
    onProductClick: (Product) -> Unit
) {
    val dateFormat = SimpleDateFormat("EEEE, MMM dd, yyyy", Locale.getDefault())

    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "📦 Products expiring on ${dateFormat.format(selectedDate)}",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            if (products.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No products expiring on this date",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 200.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(products) { product ->
                        AgendaProductCard(
                            product = product,
                            translationViewModel = translationViewModel,
                            onClick = { onProductClick(product) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductDetailsDialog(
    product: Product,
    translationViewModel: TranslationViewModel,
    onDismiss: () -> Unit
) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            TranslatableText(
                text = product.productName,
                translationViewModel = translationViewModel,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                DetailRow("Branch", product.branchName)
                DetailRow("Barcode", product.barcode)
                DetailRow("Type", product.productType)
                DetailRow("Batch", product.batch)
                DetailRow("Current Quantity", product.currentQuantity.toString())
                DetailRow("Initial Quantity", product.initialQuantity.toString())
                DetailRow("Mfg Date", dateFormat.format(Date(product.mfgDate)))
                DetailRow("Exp Date", dateFormat.format(Date(product.expireDate)))
                DetailRow("Status", if (product.isExpired) "Expired" else "Active")
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

@Composable
private fun DetailRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End
        )
    }
}

// Helper data classes and functions
data class CalendarDay(
    val date: Date,
    val isCurrentMonth: Boolean
)

private fun generateCalendarDays(currentMonth: Calendar): List<CalendarDay> {
    val days = mutableListOf<CalendarDay>()
    val calendar = currentMonth.clone() as Calendar

    // Set to first day of month
    calendar.set(Calendar.DAY_OF_MONTH, 1)

    // Get first day of week for the month
    val firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)

    // Add days from previous month to fill the first week
    val prevMonth = calendar.clone() as Calendar
    prevMonth.add(Calendar.MONTH, -1)
    prevMonth.set(Calendar.DAY_OF_MONTH, prevMonth.getActualMaximum(Calendar.DAY_OF_MONTH))

    for (i in firstDayOfWeek - 1 downTo 1) {
        val dayCalendar = prevMonth.clone() as Calendar
        dayCalendar.add(Calendar.DAY_OF_MONTH, -(i - 1))
        days.add(CalendarDay(dayCalendar.time, false))
    }

    // Add days of current month
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    for (day in 1..daysInMonth) {
        calendar.set(Calendar.DAY_OF_MONTH, day)
        days.add(CalendarDay(calendar.time, true))
    }

    // Add days from next month to fill the last week
    val nextMonth = calendar.clone() as Calendar
    nextMonth.add(Calendar.MONTH, 1)
    nextMonth.set(Calendar.DAY_OF_MONTH, 1)

    val remainingDays = 42 - days.size // 6 weeks * 7 days
    for (day in 1..remainingDays) {
        nextMonth.set(Calendar.DAY_OF_MONTH, day)
        days.add(CalendarDay(nextMonth.time, false))
    }

    return days
}

private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance()
    val cal2 = Calendar.getInstance()
    cal1.time = date1
    cal2.time = date2

    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
            cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}
