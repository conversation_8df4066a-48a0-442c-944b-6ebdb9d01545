package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.data.Product
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Translate
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.DisposableEffect
import android.speech.tts.TextToSpeech
import android.util.Log

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.*
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.ui.components.TranslatableText

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExpiredListScreen(
    products: List<Product>,
    database: AppDatabase,
    translationViewModel: TranslationViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val dateFormatter = remember { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }
    val currentDate = remember { Date() }
    val scope = rememberCoroutineScope()

    // Language-aware TTS helper
    val languageAwareTTS = remember { LanguageAwareTTSHelper(context) }

    DisposableEffect(Unit) {
        onDispose {
            languageAwareTTS.shutdown()
        }
    }

    var expandedProductId by remember { mutableStateOf<Int?>(null) }
    var searchQuery by remember { mutableStateOf("") }

    val filteredProducts = remember(products, currentDate, searchQuery) {
        products.filter { product ->
            // Show products that are either marked as expired OR have passed their expiry date
            (product.isExpired || product.expireDate < currentDate.time) &&
            searchQuery.trim().lowercase().let { query ->
                when {
                    query.isEmpty() -> true
                    product.productName.lowercase().contains(query) -> true
                    product.barcode.lowercase().contains(query) -> true
                    product.productType.lowercase().contains(query) -> true
                    product.batch.lowercase().contains(query) -> true
                    dateFormatter.format(Date(product.expireDate)).contains(query) -> true
                    else -> false
                }
            }
        }.sortedByDescending { it.expireDate }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {

        // Add a header with total count
        Text(
            text = "Total Expired Products: ${filteredProducts.size}",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.error,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Search expired products...") },
            singleLine = true,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = MaterialTheme.colorScheme.error,
                unfocusedBorderColor = MaterialTheme.colorScheme.error.copy(alpha = 0.5f)
            )
        )

        Spacer(modifier = Modifier.height(16.dp))

        if (filteredProducts.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (searchQuery.isEmpty())
                        "No expired products found"
                    else
                        "No matching expired products found",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(
                    items = filteredProducts,
                    key = { it.id }
                ) { product ->
                    ExpiredProductCard(
                        product = product,
                        database = database,
                        isExpanded = expandedProductId == product.id,
                        onExpandClick = {
                            expandedProductId = if (expandedProductId == product.id) null else product.id
                        },
                        scope = scope,
                        dateFormatter = dateFormatter,
                        translationViewModel = translationViewModel,
                        languageAwareTTS = languageAwareTTS
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ExpiredProductCard(
    product: Product,
    database: AppDatabase,
    isExpanded: Boolean,
    onExpandClick: () -> Unit,
    scope: CoroutineScope,
    dateFormatter: SimpleDateFormat,
    translationViewModel: TranslationViewModel,
    languageAwareTTS: LanguageAwareTTSHelper,
    modifier: Modifier = Modifier
) {
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    TranslatableText(
                        text = product.productName,
                        translationViewModel = translationViewModel,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                    Text(
                        text = "Expired on ${dateFormatter.format(Date(product.expireDate))}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                    )
                }

                Row {
                    // TTS Button
                    IconButton(
                        onClick = {
                            scope.launch {
                                val displayedText = translationViewModel.getTranslatedProductName(product.productName)
                                val isTranslated = displayedText != product.productName
                                languageAwareTTS.speakWithLanguageDetection(
                                    originalText = product.productName,
                                    displayedText = displayedText,
                                    isTranslated = isTranslated
                                )
                            }
                        }
                    ) {
                        Icon(
                            Icons.Default.VolumeUp,
                            contentDescription = "Speak product name",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    IconButton(onClick = { showDeleteConfirmation = true }) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete product",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }

                    IconButton(onClick = onExpandClick) {
                        Icon(
                            if (isExpanded) Icons.Default.KeyboardArrowUp
                            else Icons.Default.KeyboardArrowDown,
                            contentDescription = if (isExpanded) "Show less" else "Show more",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            if (isExpanded) {
                Spacer(modifier = Modifier.height(8.dp))
                Divider(color = MaterialTheme.colorScheme.error.copy(alpha = 0.2f))
                Spacer(modifier = Modifier.height(8.dp))

                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    DetailRow("Barcode", formatBarcode(product.barcode))
                    DetailRow("Batch", formatBatch(product.batch))
                    DetailRow("Quantity", "${product.currentQuantity}/${product.initialQuantity}")
                    DetailRow("Manufacturing Date", dateFormatter.format(product.mfgDate))
                    DetailRow("Branch", product.branchName)
                }
            }
        }
    }

    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Confirm Deletion") },
            text = {
                Text("Are you sure you want to delete '${product.productName}'? This action cannot be undone.")
            },
            confirmButton = {
                Button(
                    onClick = {
                        scope.launch {
                            database.productDao().deleteProduct(product)
                        }
                        showDeleteConfirmation = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmation = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
    }
}

// Utility function to format barcode for proper display
private fun formatBarcode(barcode: String): String {
    return try {
        // If barcode is in scientific notation or needs formatting
        if (barcode.contains("E") || barcode.contains("e")) {
            // Convert scientific notation to full number string
            val number = barcode.toDoubleOrNull()
            if (number != null) {
                // Format as long integer to avoid decimal places
                String.format("%.0f", number)
            } else {
                barcode // Return original if conversion fails
            }
        } else {
            // Return barcode as-is if it's already properly formatted
            barcode
        }
    } catch (e: Exception) {
        // Return original barcode if any error occurs
        barcode
    }
}

// Utility function to format batch number in 00:00 format
private fun formatBatch(batch: String): String {
    return try {
        // First, handle scientific notation if present
        val cleanBatch = if (batch.contains("E") || batch.contains("e")) {
            val number = batch.toDoubleOrNull()
            if (number != null) {
                String.format("%.0f", number)
            } else {
                batch
            }
        } else if (batch.contains(".") && batch.toDoubleOrNull() != null) {
            // Handle decimal numbers that should be integers (like 123.0 -> 123)
            val number = batch.toDouble()
            if (number == number.toLong().toDouble()) {
                String.format("%.0f", number)
            } else {
                batch
            }
        } else {
            batch
        }

        // Now format to 00:00 pattern
        formatBatchTo00Format(cleanBatch)

    } catch (e: Exception) {
        // Return original batch if any error occurs
        batch
    }
}

// Format batch to 00:00 pattern
private fun formatBatchTo00Format(batch: String): String {
    // If already in 00:00 format, return as-is
    if (batch.matches(Regex("\\d{2}:\\d{2}"))) {
        return batch
    }

    // Extract only digits from the batch
    val digitsOnly = batch.filter { it.isDigit() }

    return when {
        digitsOnly.length >= 4 -> {
            // Take first 4 digits and format as 00:00
            "${digitsOnly.take(2)}:${digitsOnly.substring(2, 4)}"
        }
        digitsOnly.length == 3 -> {
            // 3 digits: format as 0X:XX
            "0${digitsOnly.take(1)}:${digitsOnly.substring(1)}"
        }
        digitsOnly.length == 2 -> {
            // 2 digits: format as 00:XX
            "00:${digitsOnly}"
        }
        digitsOnly.length == 1 -> {
            // 1 digit: format as 00:0X
            "00:0${digitsOnly}"
        }
        digitsOnly.isEmpty() -> {
            // No digits found, check if it's already a text batch
            if (batch.isNotBlank()) {
                batch // Return original text batch
            } else {
                "00:00" // Default format
            }
        }
        else -> {
            // More than 4 digits, take first 4
            "${digitsOnly.take(2)}:${digitsOnly.substring(2, 4)}"
        }
    }
}

/**
 * Language-aware TTS helper that speaks in Arabic for untranslated text
 * and English for translated text
 */
class LanguageAwareTTSHelper(context: android.content.Context) {
    private var arabicTTS: TextToSpeech? = null
    private var englishTTS: TextToSpeech? = null
    private var isArabicReady = false
    private var isEnglishReady = false

    init {
        // Initialize Arabic TTS
        arabicTTS = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                val arabicLocale = java.util.Locale("ar")
                val result = arabicTTS?.setLanguage(arabicLocale)
                when (result) {
                    TextToSpeech.LANG_MISSING_DATA,
                    TextToSpeech.LANG_NOT_SUPPORTED -> {
                        Log.e("LanguageAwareTTS", "Arabic language not supported")
                    }
                    else -> {
                        isArabicReady = true
                        arabicTTS?.setPitch(1.0f)
                        arabicTTS?.setSpeechRate(0.8f)
                        Log.d("LanguageAwareTTS", "Arabic TTS initialized successfully")
                    }
                }
            } else {
                Log.e("LanguageAwareTTS", "Arabic TTS initialization failed")
            }
        }

        // Initialize English TTS
        englishTTS = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                val englishLocale = java.util.Locale.ENGLISH
                val result = englishTTS?.setLanguage(englishLocale)
                when (result) {
                    TextToSpeech.LANG_MISSING_DATA,
                    TextToSpeech.LANG_NOT_SUPPORTED -> {
                        Log.e("LanguageAwareTTS", "English language not supported")
                    }
                    else -> {
                        isEnglishReady = true
                        englishTTS?.setPitch(1.0f)
                        englishTTS?.setSpeechRate(0.9f)
                        Log.d("LanguageAwareTTS", "English TTS initialized successfully")
                    }
                }
            } else {
                Log.e("LanguageAwareTTS", "English TTS initialization failed")
            }
        }
    }

    /**
     * Speak text with automatic language detection based on translation status
     * @param originalText The original product name (usually Arabic)
     * @param displayedText The text currently displayed (translated or original)
     * @param isTranslated Whether the text has been translated
     */
    fun speakWithLanguageDetection(
        originalText: String,
        displayedText: String,
        isTranslated: Boolean
    ) {
        if (isTranslated) {
            // Text is translated to English, use English TTS
            if (isEnglishReady) {
                Log.d("LanguageAwareTTS", "Speaking in English: '$displayedText'")
                englishTTS?.speak(displayedText, TextToSpeech.QUEUE_FLUSH, null, null)
            } else {
                Log.w("LanguageAwareTTS", "English TTS not ready, falling back to Arabic")
                if (isArabicReady) {
                    arabicTTS?.speak(originalText, TextToSpeech.QUEUE_FLUSH, null, null)
                }
            }
        } else {
            // Text is not translated (original Arabic), use Arabic TTS
            if (isArabicReady) {
                Log.d("LanguageAwareTTS", "Speaking in Arabic: '$originalText'")
                arabicTTS?.speak(originalText, TextToSpeech.QUEUE_FLUSH, null, null)
            } else {
                Log.w("LanguageAwareTTS", "Arabic TTS not ready")
            }
        }
    }

    /**
     * Shutdown both TTS engines
     */
    fun shutdown() {
        arabicTTS?.stop()
        arabicTTS?.shutdown()
        englishTTS?.stop()
        englishTTS?.shutdown()
        Log.d("LanguageAwareTTS", "TTS engines shut down")
    }
}