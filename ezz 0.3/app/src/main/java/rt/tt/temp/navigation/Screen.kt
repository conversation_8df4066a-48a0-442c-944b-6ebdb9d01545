package rt.tt.temp.navigation

sealed class Screen(val route: String) {
    object Dashboard : Screen("dashboard")
    object AllItems : Screen("all_items")
    object NewEntry : Screen("new_entry")
    object ExpiredList : Screen("expired_list")
    object Export : Screen("export")
    object GoogleDriveSync : Screen("google_drive_sync")
    object GoogleSheets : Screen("google_sheets")
    object Notifications : Screen("notifications")
    object Analytics : Screen("analytics")
    object Attendance : Screen("attendance")
    object Incentive : Screen("incentive")
    object Reports : Screen("reports")
    object MultiUser : Screen("multi_user")
    object Settings : Screen("settings")
    object About : Screen("about")
    object Threshold : Screen("threshold")
    object AdminDashboard : Screen("admin_dashboard")
    object Calendar : Screen("calendar")
}