package rt.tt.temp.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.collectAsState
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import rt.tt.temp.ui.screens.ExportScreen
import rt.tt.temp.data.Product
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import android.util.Log
import java.io.File
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.ui.screens.* // Import all screens

// Use explicit import instead of commented one
import rt.tt.temp.utils.TimeUtils
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import androidx.lifecycle.viewmodel.compose.viewModel

@Composable
fun SetupNavGraph(
    navController: NavHostController,
    translationViewModel: TranslationViewModel
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val database = remember { AppDatabase.getInstance(context) }

    val currentDate = remember { TimeUtils.getArabianStandardTime() }

    val activeProductsFlow: Flow<List<Product>> = remember {
        database.productDao().getAllActiveProducts(System.currentTimeMillis())
    }
    val activeProducts = activeProductsFlow.collectAsState(initial = emptyList()).value

    // Add this for expired products
    val expiredProductsFlow: Flow<List<Product>> = remember {
        database.productDao().getAllExpiredProducts(System.currentTimeMillis())
    }
    val expiredProducts = expiredProductsFlow.collectAsState(initial = emptyList()).value

    NavHost(
        navController = navController,
        startDestination = Screen.Dashboard.route
    ) {
        composable(route = Screen.Dashboard.route) {
            DashboardScreen(
                products = activeProducts,
                currentDate = currentDate,
                translationViewModel = translationViewModel
            )
        }
        composable(
            route = Screen.AllItems.route,
            enterTransition = { EnterTransition.None },
            exitTransition = { ExitTransition.None }
        ) {
            AllItemsScreen(
                products = activeProducts,
                database = database,
                currentDate = currentDate,
                translationViewModel = translationViewModel
            )
        }
        composable(route = Screen.NewEntry.route) {
            NewEntryScreen(
                database = database,
                onSave = { branchName, productName, productType, barcode, batch, quantity, mfgDate, expDate ->
                    scope.launch {
                        try {
                            val product = Product(
                                id = 0, // Default value for new product
                                productName = productName,
                                barcode = barcode,
                                productType = productType, // Use actual form value
                                batch = batch, // Use actual form value
                                branchName = branchName, // Use actual form value
                                currentQuantity = quantity,
                                initialQuantity = quantity,
                                mfgDate = mfgDate.time, // Convert Date to Long timestamp
                                expireDate = expDate.time, // Convert Date to Long timestamp
                                createdAt = System.currentTimeMillis(),
                                isExpired = false
                            )

                            database.productDao().insertProduct(product)
                            Log.d("NavGraph", "Product saved successfully: $product")

                            // Removed the auto-navigation code:
                            // navController.navigate(Screen.AllItems.route) {
                            //     popUpTo(Screen.Dashboard.route)
                            //     launchSingleTop = true
                            // }
                        } catch (e: Exception) {
                            Log.e("NavGraph", "Error saving product", e)
                        }
                    }
                }
            )
        }
        composable(route = Screen.ExpiredList.route) {
            ExpiredListScreen(
                products = expiredProducts,  // Pass expired products here
                database = database,
                translationViewModel = translationViewModel
            )
        }
        composable(route = Screen.Export.route) {
            ExportScreen(
                products = activeProducts,
                database = database,
                onBackupComplete = { destinationFile: File ->
                    scope.launch {
                        rt.tt.temp.utils.BackupUtils.createBackup(
                            context = context,
                            database = database,
                            destinationFile = destinationFile
                        )
                    }
                }
            )
        }
        composable(route = Screen.GoogleDriveSync.route) {
            GoogleDriveSyncScreen()
        }
        composable(route = Screen.GoogleSheets.route) {
            GoogleSheetsScreen()
        }
        composable(route = Screen.Notifications.route) {
            NotificationsScreen()
        }
        composable(route = Screen.Settings.route) {
            SettingsScreen(translationViewModel = translationViewModel)
        }
        composable(route = Screen.About.route) {
            AboutScreen()
        }
        composable(route = Screen.AdminDashboard.route) {
            AdminDashboardScreen()
        }
        composable(route = Screen.Analytics.route) {
            AnalyticsScreen()
        }
        composable(route = Screen.Attendance.route) {
            AttendanceScreen()
        }
        composable(route = Screen.Incentive.route) {
            IncentiveScreen()
        }
        composable(route = Screen.Threshold.route) {
            ThresholdScreen(
                database = database
            )
        }
        composable(route = Screen.Calendar.route) {
            CalendarScreen(
                database = database,
                translationViewModel = translationViewModel
            )
        }
    }
}