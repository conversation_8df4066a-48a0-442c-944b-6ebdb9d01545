package rt.tt.temp.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import rt.tt.temp.ui.viewmodels.GoogleSheetsViewModel
import rt.tt.temp.managers.GoogleSheetsManager
import rt.tt.temp.data.Product
import kotlinx.coroutines.launch
import android.util.Log
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.draw.shadow
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

@Composable
fun AdminDashboardScreen() {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val scrollState = rememberScrollState()

    // Initialize GoogleSheetsManager
    LaunchedEffect(Unit) {
        GoogleSheetsManager.getInstance().initialize(context)
    }

    val viewModel: GoogleSheetsViewModel = viewModel {
        GoogleSheetsManager.getInstance().getViewModel() ?: GoogleSheetsViewModel(context)
    }

    // Collect state from ViewModel
    val isConnected by viewModel.isConnected.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val lastSyncTime by viewModel.lastSyncTime.collectAsState()
    val availableBranches by viewModel.availableBranches.collectAsState()
    val branchProductCounts by viewModel.branchProductCounts.collectAsState()

    // Admin dashboard state
    var nearExpiryData by remember { mutableStateOf<Map<String, List<ExpiryData>>>(emptyMap()) }
    var isLoadingData by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Branch data from Google Sheets
    var adminBranches by remember { mutableStateOf<List<String>>(emptyList()) }
    var adminBranchProductCounts by remember { mutableStateOf<Map<String, Int>>(emptyMap()) }

    // Branch details overlay state
    var selectedBranch by remember { mutableStateOf<String?>(null) }
    var branchProducts by remember { mutableStateOf<List<Product>>(emptyList()) }
    var isLoadingBranchProducts by remember { mutableStateOf(false) }

    // Load data from Google Sheets (READ-ONLY - does not modify local database)
    fun loadAdminData() {
        scope.launch {
            isLoadingData = true
            errorMessage = null
            try {
                Log.d("AdminDashboard", "Loading admin data from Google Sheets (read-only)...")
                Log.d("AdminDashboard", "Connection status: ${isConnected}")

                // Check connection status first
                if (!isConnected) {
                    Log.w("AdminDashboard", "Not connected to Google Sheets - attempting to load anyway")
                }

                // Always try Google Sheets first (READ-ONLY)
                Log.d("AdminDashboard", "=== CALLING GOOGLE SHEETS API ===")
                val allProducts = viewModel.sheetsService.getAllProducts()
                Log.d("AdminDashboard", "=== GOOGLE SHEETS API RESPONSE ===")
                Log.d("AdminDashboard", "Google Sheets API returned ${allProducts.size} products")

                // Log sample products for debugging
                if (allProducts.isNotEmpty()) {
                    allProducts.take(3).forEachIndexed { index, product ->
                        Log.d("AdminDashboard", "Sample product $index: ${product.productName} - ${product.branchName} - Expires: ${java.util.Date(product.expireDate)}")
                    }
                } else {
                    Log.d("AdminDashboard", "No products returned from Google Sheets API")
                }

                // Use Google Sheets data regardless of whether it's empty or not
                nearExpiryData = calculateExpiryDataFromProducts(allProducts)

                // Calculate branch data from Google Sheets products
                adminBranches = allProducts.map { it.branchName }.distinct().sorted()
                adminBranchProductCounts = allProducts.groupBy { it.branchName }.mapValues { it.value.size }

                Log.d("AdminDashboard", "Calculated expiry data for ${nearExpiryData.size} branches")
                Log.d("AdminDashboard", "Available branches: ${adminBranches.joinToString(", ")}")
                nearExpiryData.forEach { (branch, expiryList) ->
                    Log.d("AdminDashboard", "Branch $branch: ${expiryList.sumOf { it.count }} total products")
                }
                adminBranchProductCounts.forEach { (branch, count) ->
                    Log.d("AdminDashboard", "Branch $branch: $count total products")
                }
                Log.d("AdminDashboard", "Loaded admin display data: ${allProducts.size} products from Google Sheets (no local DB changes)")

                // If Google Sheets is empty, show appropriate message but don't fall back to local DB
                if (allProducts.isEmpty()) {
                    adminBranches = emptyList()
                    adminBranchProductCounts = emptyMap()
                    Log.w("AdminDashboard", "Google Sheets has no products - showing empty state")
                    Log.w("AdminDashboard", "This means either: 1) Google Sheets is actually empty, 2) API call succeeded but returned no data, 3) Data parsing failed")
                }

            } catch (e: Exception) {
                Log.e("AdminDashboard", "Failed to load admin data from Google Sheets", e)
                errorMessage = "Failed to load data from Google Sheets: ${e.message}"

                // Only use local database as fallback if Google Sheets connection completely fails
                try {
                    val localProducts = viewModel.database.productDao().getAllProducts()
                    nearExpiryData = calculateExpiryDataFromProducts(localProducts)

                    // Calculate branch data from local products
                    adminBranches = localProducts.map { it.branchName }.distinct().sorted()
                    adminBranchProductCounts = localProducts.groupBy { it.branchName }.mapValues { it.value.size }

                    Log.d("AdminDashboard", "Using local fallback data: ${localProducts.size} products (Google Sheets connection failed)")
                    Log.d("AdminDashboard", "Local branches: ${adminBranches.joinToString(", ")}")
                    errorMessage = "Using local data - Google Sheets connection failed: ${e.message}"
                } catch (localE: Exception) {
                    Log.e("AdminDashboard", "Failed to load fallback data from local database too", localE)
                    errorMessage = "Failed to load data from both Google Sheets and local database"
                    nearExpiryData = emptyMap()
                    adminBranches = emptyList()
                    adminBranchProductCounts = emptyMap()
                }
            } finally {
                isLoadingData = false
            }
        }
    }

    // Load branch products function (READ-ONLY - does not modify local database)
    fun loadBranchProducts(branchName: String) {
        scope.launch {
            isLoadingBranchProducts = true
            try {
                Log.d("AdminDashboard", "Loading products for branch: $branchName (read-only)")

                // Always fetch from Google Sheets first (READ-ONLY)
                val realProducts = viewModel.sheetsService.getProductsByBranch(branchName)

                // Use Google Sheets data regardless of whether it's empty or not
                branchProducts = realProducts.sortedBy { it.expireDate }
                selectedBranch = branchName
                Log.d("AdminDashboard", "Loaded ${branchProducts.size} products for $branchName from Google Sheets (display only, no DB changes)")

                // If Google Sheets is empty for this branch, show appropriate message
                if (realProducts.isEmpty()) {
                    Log.d("AdminDashboard", "Google Sheets has no products for branch $branchName - showing empty state")
                }

            } catch (e: Exception) {
                Log.e("AdminDashboard", "Failed to load branch products from Google Sheets", e)

                // Only use local database as fallback if Google Sheets connection completely fails
                try {
                    val localProducts = viewModel.database.productDao().getProductsByBranch(branchName)
                    branchProducts = localProducts.sortedBy { it.expireDate }
                    selectedBranch = branchName
                    Log.d("AdminDashboard", "Using local fallback data: ${branchProducts.size} products for $branchName (Google Sheets connection failed)")
                } catch (localE: Exception) {
                    Log.e("AdminDashboard", "Failed to load branch products from local database too", localE)
                    branchProducts = emptyList()
                    selectedBranch = branchName
                }
            } finally {
                isLoadingBranchProducts = false
            }
        }
    }

    // Auto-refresh data every time Admin Dashboard is opened
    LaunchedEffect(Unit) {
        Log.d("AdminDashboard", "Admin Dashboard opened - auto-refreshing data from Google Sheets")
        loadAdminData()
    }

    // Also refresh when connection status changes
    LaunchedEffect(isConnected) {
        if (isConnected) {
            Log.d("AdminDashboard", "Google Sheets connected - refreshing admin data")
            loadAdminData()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        // Header Section
        AdminDashboardHeader(
            isConnected = isConnected,
            lastSyncTime = lastSyncTime,
            onRefresh = { loadAdminData() },
            isLoading = isLoadingData
        )

        // Debug Section (temporary for troubleshooting)
        if (!isConnected || adminBranches.isEmpty()) {
            DebugSection(
                isConnected = isConnected,
                branchCount = adminBranches.size,
                branches = adminBranches,
                onTestConnection = {
                    scope.launch {
                        val testResult = viewModel.sheetsService.testConnection()
                        Log.d("AdminDashboard", "Manual connection test result: $testResult")
                    }
                },
                onTestDataFetch = { loadAdminData() },
                onDiagnose = {
                    scope.launch {
                        Log.d("AdminDashboard", "=== MANUAL DIAGNOSTICS ===")
                        Log.d("AdminDashboard", "Current admin branches: ${adminBranches.joinToString(", ")}")
                        Log.d("AdminDashboard", "Current branch counts: $adminBranchProductCounts")
                        try {
                            val products = viewModel.sheetsService.getAllProducts()
                            Log.d("AdminDashboard", "Manual fetch returned ${products.size} products")
                            val branches = products.map { it.branchName }.distinct()
                            Log.d("AdminDashboard", "Branches in fetched data: ${branches.joinToString(", ")}")
                            products.take(5).forEach { product ->
                                Log.d("AdminDashboard", "Product: ${product.productName} | Branch: ${product.branchName} | Barcode: ${product.barcode}")
                            }
                        } catch (e: Exception) {
                            Log.e("AdminDashboard", "Manual fetch failed", e)
                        }
                    }
                }
            )
        }

        Spacer(modifier = Modifier.height(20.dp))

        if (!isConnected) {
            // Connection Required Card
            ConnectionRequiredCard()
        } else if (isLoadingData) {
            // Loading State
            LoadingDataCard()
        } else if (errorMessage != null) {
            // Error State
            ErrorCard(
                message = errorMessage!!,
                onRetry = { loadAdminData() }
            )
        } else {
            // Dashboard Content
            AdminDashboardContent(
                nearExpiryData = nearExpiryData,
                availableBranches = adminBranches,
                branchProductCounts = adminBranchProductCounts,
                onBranchClick = { branchName ->
                    loadBranchProducts(branchName)
                }
            )
        }

        // Add bottom padding for scroll
        Spacer(modifier = Modifier.height(20.dp))
    }

    // Branch Details Overlay
    selectedBranch?.let { branchName ->
        BranchDetailsOverlay(
            branchName = branchName,
            products = branchProducts,
            isLoading = isLoadingBranchProducts,
            onDismiss = {
                selectedBranch = null
                branchProducts = emptyList()
            }
        )
    }
}

@Composable
private fun AdminDashboardHeader(
    isConnected: Boolean,
    lastSyncTime: String?,
    onRefresh: () -> Unit,
    isLoading: Boolean
) {
    ElevatedCard(
        modifier = Modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(16.dp),
                spotColor = MaterialTheme.colorScheme.error.copy(alpha = 0.25f)
            ),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 6.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color.Transparent
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.9f),
                            MaterialTheme.colorScheme.error.copy(alpha = 0.1f)
                        ),
                        start = Offset(0f, 0f),
                        end = Offset(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY)
                    ),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(20.dp)
        ) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Admin Dashboard",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = "Live analytics from Google Sheets (auto-refreshed, read-only)",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.8f)
                        )
                    }

                    // Refresh Button
                    IconButton(
                        onClick = onRefresh,
                        enabled = isConnected && !isLoading
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "Refresh",
                                tint = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // Status Row
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (isConnected) Icons.Default.CloudDone else Icons.Default.CloudOff,
                        contentDescription = null,
                        tint = if (isConnected) Color(0xFF4CAF50) else MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (isConnected) "Connected to Google Sheets" else "Not connected to Google Sheets",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.9f)
                    )
                }

                if (lastSyncTime != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Last sync: $lastSyncTime",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ConnectionRequiredCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.CloudOff,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Google Sheets Connection Required",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Please connect to Google Sheets from the Google Sheets screen to view admin analytics.",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun LoadingDataCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Loading Admin Data",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Fetching branch-wise expiry data from Google Sheets...",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ErrorCard(
    message: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Error Loading Data",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Retry")
            }
        }
    }
}

@Composable
private fun AdminDashboardContent(
    nearExpiryData: Map<String, List<ExpiryData>>,
    availableBranches: List<String>,
    branchProductCounts: Map<String, Int>,
    onBranchClick: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Branch Overview Cards
        BranchOverviewSection(
            availableBranches = availableBranches,
            branchProductCounts = branchProductCounts,
            nearExpiryData = nearExpiryData,
            onBranchClick = onBranchClick
        )

        // Near Expiry Charts
        NearExpiryChartsSection(nearExpiryData = nearExpiryData)
    }
}

@Composable
private fun BranchOverviewSection(
    availableBranches: List<String>,
    branchProductCounts: Map<String, Int>,
    nearExpiryData: Map<String, List<ExpiryData>>,
    onBranchClick: (String) -> Unit
) {
    Text(
        text = "Branch Overview",
        style = MaterialTheme.typography.titleLarge,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 8.dp)
    )

    availableBranches.forEach { branch ->
        val productCount = branchProductCounts[branch] ?: 0
        val expiryData = nearExpiryData[branch] ?: emptyList()
        val nearExpiryCount = expiryData.sumOf { it.count }

        BranchCard(
            branchName = branch,
            totalProducts = productCount,
            nearExpiryCount = nearExpiryCount,
            expiryData = expiryData,
            onClick = { onBranchClick(branch) }
        )
    }
}

@Composable
private fun BranchCard(
    branchName: String,
    totalProducts: Int,
    nearExpiryCount: Int,
    expiryData: List<ExpiryData>,
    onClick: () -> Unit
) {
    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.clickable { onClick() }
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = branchName,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.Default.OpenInNew,
                            contentDescription = "View details",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    Text(
                        text = "$totalProducts total products • Click to view details",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "$nearExpiryCount",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = if (nearExpiryCount > 0) MaterialTheme.colorScheme.error else Color(0xFF4CAF50)
                    )
                    Text(
                        text = "near expiry",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            if (expiryData.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                SimpleBarChart(expiryData = expiryData)
            }
        }
    }
}

@Composable
private fun NearExpiryChartsSection(nearExpiryData: Map<String, List<ExpiryData>>) {
    Text(
        text = "Near Expiry Analytics",
        style = MaterialTheme.typography.titleLarge,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 8.dp)
    )

    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Products Expiring in Next 30 Days",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Combined chart for all branches
            CombinedExpiryChart(nearExpiryData = nearExpiryData)
        }
    }
}

@Composable
private fun SimpleBarChart(expiryData: List<ExpiryData>) {
    val maxCount = expiryData.maxOfOrNull { it.count } ?: 1

    Column {
        expiryData.forEach { data ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = data.category,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.width(80.dp)
                )

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(20.dp)
                        .background(
                            MaterialTheme.colorScheme.surfaceVariant,
                            RoundedCornerShape(4.dp)
                        )
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(data.count.toFloat() / maxCount)
                            .background(
                                data.color,
                                RoundedCornerShape(4.dp)
                            )
                    )
                }

                Text(
                    text = "${data.count}",
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.width(40.dp),
                    textAlign = TextAlign.End
                )
            }
        }
    }
}

@Composable
private fun CombinedExpiryChart(nearExpiryData: Map<String, List<ExpiryData>>) {
    val combinedData = mutableMapOf<String, Int>()

    nearExpiryData.values.forEach { branchData ->
        branchData.forEach { expiryData ->
            combinedData[expiryData.category] = (combinedData[expiryData.category] ?: 0) + expiryData.count
        }
    }

    val chartData = combinedData.map { (category, count) ->
        ExpiryData(
            category = category,
            count = count,
            color = when (category) {
                "Expired" -> Color(0xFFE53935)
                "1-7 days" -> Color(0xFFFF9800)
                "8-15 days" -> Color(0xFFFFC107)
                "16-30 days" -> Color(0xFF4CAF50)
                else -> MaterialTheme.colorScheme.primary
            }
        )
    }.sortedByDescending { it.count }

    if (chartData.isNotEmpty()) {
        SimpleBarChart(expiryData = chartData)
    } else {
        Text(
            text = "No expiry data available",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

// Data classes
data class ExpiryData(
    val category: String,
    val count: Int,
    val color: Color
)

@Composable
private fun BranchDetailsOverlay(
    branchName: String,
    products: List<Product>,
    isLoading: Boolean,
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.7f))
                .clickable { onDismiss() },
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .fillMaxHeight(0.85f)
                    .clickable(enabled = false) { /* Prevent dismiss on card click */ },
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 16.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Header
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                brush = Brush.linearGradient(
                                    colors = listOf(
                                        MaterialTheme.colorScheme.primaryContainer,
                                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                                    )
                                ),
                                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                            )
                            .padding(20.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column {
                                Text(
                                    text = "$branchName Branch",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                                Text(
                                    text = "Products sorted by expiry date",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                                )
                            }

                            IconButton(
                                onClick = onDismiss
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "Close",
                                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            }
                        }
                    }

                    // Content
                    if (isLoading) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                CircularProgressIndicator(
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "Loading products...",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    } else if (products.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Inventory,
                                    contentDescription = null,
                                    modifier = Modifier.size(48.dp),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(
                                    text = "No products found",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = "This branch has no products or data is not available",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    } else {
                        // Products List
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(
                                items = products,
                                key = { it.id }
                            ) { product ->
                                ProductDetailCard(product = product)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductDetailCard(product: Product) {
    val currentDate = System.currentTimeMillis()
    val daysUntilExpiry = TimeUnit.MILLISECONDS.toDays(product.expireDate - currentDate).toInt()
    val dateFormat = remember { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }

    // Determine color based on expiry status
    val (backgroundColor, textColor, statusText) = when {
        daysUntilExpiry < 0 -> Triple(
            Color(0xFFFFEBEE), // Light red background
            Color(0xFFE53935), // Red text
            "Expired ${-daysUntilExpiry} days ago"
        )
        daysUntilExpiry <= 7 -> Triple(
            Color(0xFFFFF3E0), // Light orange background
            Color(0xFFFF9800), // Orange text
            "Expires in $daysUntilExpiry days"
        )
        daysUntilExpiry <= 15 -> Triple(
            Color(0xFFFFFDE7), // Light yellow background
            Color(0xFFFFC107), // Yellow text
            "Expires in $daysUntilExpiry days"
        )
        daysUntilExpiry <= 30 -> Triple(
            Color(0xFFE8F5E9), // Light green background
            Color(0xFF4CAF50), // Green text
            "Expires in $daysUntilExpiry days"
        )
        else -> Triple(
            MaterialTheme.colorScheme.surfaceVariant,
            MaterialTheme.colorScheme.onSurfaceVariant,
            "Expires in $daysUntilExpiry days"
        )
    }

    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.elevatedCardColors(
            containerColor = backgroundColor
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Product name and status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = product.productName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "${product.productType} • Batch: ${formatBatch(product.batch)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Status badge
                Box(
                    modifier = Modifier
                        .background(
                            textColor.copy(alpha = 0.1f),
                            RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = statusText,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        color = textColor
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Product details
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ProductDetailItem(
                    label = "Barcode",
                    value = formatBarcode(product.barcode),
                    icon = Icons.Default.QrCode
                )
                ProductDetailItem(
                    label = "Quantity",
                    value = "${product.currentQuantity}/${product.initialQuantity}",
                    icon = Icons.Default.Inventory
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ProductDetailItem(
                    label = "Mfg Date",
                    value = dateFormat.format(Date(product.mfgDate)),
                    icon = Icons.Default.CalendarToday
                )
                ProductDetailItem(
                    label = "Exp Date",
                    value = dateFormat.format(Date(product.expireDate)),
                    icon = Icons.Default.Event,
                    valueColor = textColor
                )
            }
        }
    }
}

@Composable
private fun ProductDetailItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    valueColor: Color = MaterialTheme.colorScheme.onSurface
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(16.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.width(6.dp))
        Column {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = value,
                style = if (label == "Barcode") {
                    // Use monospace font for barcodes for better readability
                    MaterialTheme.typography.bodyMedium.copy(
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                } else {
                    MaterialTheme.typography.bodyMedium
                },
                fontWeight = FontWeight.Medium,
                color = valueColor,
                maxLines = if (label == "Barcode") 2 else 1, // Allow barcode to wrap if very long
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

// Calculate expiry data from real products
private fun calculateExpiryDataFromProducts(products: List<Product>): Map<String, List<ExpiryData>> {
    val currentTime = System.currentTimeMillis()
    val dayInMillis = 24 * 60 * 60 * 1000L

    // Group products by branch
    val productsByBranch = products.groupBy { it.branchName }

    return productsByBranch.mapValues { (branchName, branchProducts) ->
        // Count products in each expiry category
        val expired = branchProducts.count { it.expireDate < currentTime }
        val expiring1to7 = branchProducts.count {
            val daysUntilExpiry = (it.expireDate - currentTime) / dayInMillis
            daysUntilExpiry in 1..7
        }
        val expiring8to15 = branchProducts.count {
            val daysUntilExpiry = (it.expireDate - currentTime) / dayInMillis
            daysUntilExpiry in 8..15
        }
        val expiring16to30 = branchProducts.count {
            val daysUntilExpiry = (it.expireDate - currentTime) / dayInMillis
            daysUntilExpiry in 16..30
        }

        // Create ExpiryData list for this branch
        listOf(
            ExpiryData("Expired", expired, Color(0xFFE53935)),
            ExpiryData("1-7 days", expiring1to7, Color(0xFFFF9800)),
            ExpiryData("8-15 days", expiring8to15, Color(0xFFFFC107)),
            ExpiryData("16-30 days", expiring16to30, Color(0xFF4CAF50))
        ).filter { it.count > 0 } // Only include categories with products
    }
}

// Mock data generators for demonstration (kept as fallback)
private fun generateMockExpiryData(): Map<String, List<ExpiryData>> {
    return mapOf(
        "Warehouse" to listOf(
            ExpiryData("Expired", 5, Color(0xFFE53935)),
            ExpiryData("1-7 days", 12, Color(0xFFFF9800)),
            ExpiryData("8-15 days", 8, Color(0xFFFFC107)),
            ExpiryData("16-30 days", 15, Color(0xFF4CAF50))
        ),
        "Shifa" to listOf(
            ExpiryData("Expired", 2, Color(0xFFE53935)),
            ExpiryData("1-7 days", 7, Color(0xFFFF9800)),
            ExpiryData("8-15 days", 5, Color(0xFFFFC107)),
            ExpiryData("16-30 days", 10, Color(0xFF4CAF50))
        ),
        "Baqqa" to listOf(
            ExpiryData("Expired", 3, Color(0xFFE53935)),
            ExpiryData("1-7 days", 9, Color(0xFFFF9800)),
            ExpiryData("8-15 days", 6, Color(0xFFFFC107)),
            ExpiryData("16-30 days", 12, Color(0xFF4CAF50))
        ),
        "Jumlah" to listOf(
            ExpiryData("Expired", 1, Color(0xFFE53935)),
            ExpiryData("1-7 days", 4, Color(0xFFFF9800)),
            ExpiryData("8-15 days", 3, Color(0xFFFFC107)),
            ExpiryData("16-30 days", 8, Color(0xFF4CAF50))
        )
    )
}

// Utility function to format barcode for proper display
private fun formatBarcode(barcode: String): String {
    return try {
        // If barcode is in scientific notation or needs formatting
        if (barcode.contains("E") || barcode.contains("e")) {
            // Convert scientific notation to full number string
            val number = barcode.toDoubleOrNull()
            if (number != null) {
                // Format as long integer to avoid decimal places
                String.format("%.0f", number)
            } else {
                barcode // Return original if conversion fails
            }
        } else {
            // Return barcode as-is if it's already properly formatted
            barcode
        }
    } catch (e: Exception) {
        // Return original barcode if any error occurs
        barcode
    }
}

// Utility function to format batch number in 00:00 format like All Items screen
private fun formatBatch(batch: String): String {
    return try {
        // First, handle scientific notation if present
        val cleanBatch = if (batch.contains("E") || batch.contains("e")) {
            val number = batch.toDoubleOrNull()
            if (number != null) {
                String.format("%.0f", number)
            } else {
                batch
            }
        } else if (batch.contains(".") && batch.toDoubleOrNull() != null) {
            // Handle decimal numbers that should be integers (like 123.0 -> 123)
            val number = batch.toDouble()
            if (number == number.toLong().toDouble()) {
                String.format("%.0f", number)
            } else {
                batch
            }
        } else {
            batch
        }

        // Now format to 00:00 pattern (same as NewEntryScreen)
        formatBatchTo00Format(cleanBatch)

    } catch (e: Exception) {
        // Return original batch if any error occurs
        batch
    }
}

// Format batch to 00:00 pattern (same logic as NewEntryScreen)
private fun formatBatchTo00Format(batch: String): String {
    // If already in 00:00 format, return as-is
    if (batch.matches(Regex("\\d{2}:\\d{2}"))) {
        return batch
    }

    // Extract only digits from the batch
    val digitsOnly = batch.filter { it.isDigit() }

    return when {
        digitsOnly.length >= 4 -> {
            // Take first 4 digits and format as 00:00
            "${digitsOnly.take(2)}:${digitsOnly.substring(2, 4)}"
        }
        digitsOnly.length == 3 -> {
            // 3 digits: format as 0X:XX
            "0${digitsOnly.take(1)}:${digitsOnly.substring(1)}"
        }
        digitsOnly.length == 2 -> {
            // 2 digits: format as 00:XX
            "00:${digitsOnly}"
        }
        digitsOnly.length == 1 -> {
            // 1 digit: format as 00:0X
            "00:0${digitsOnly}"
        }
        digitsOnly.isEmpty() -> {
            // No digits found, check if it's already a text batch
            if (batch.isNotBlank()) {
                batch // Return original text batch
            } else {
                "00:00" // Default format
            }
        }
        else -> {
            // More than 4 digits, take first 4
            "${digitsOnly.take(2)}:${digitsOnly.substring(2, 4)}"
        }
    }
}

@Composable
private fun DebugSection(
    isConnected: Boolean,
    branchCount: Int,
    branches: List<String>,
    onTestConnection: () -> Unit,
    onTestDataFetch: () -> Unit,
    onDiagnose: () -> Unit
) {
    ElevatedCard(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "🔧 Debug Tools",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Connection: ${if (isConnected) "✅ Connected" else "❌ Not Connected"}",
                style = MaterialTheme.typography.bodyMedium
            )

            Text(
                text = "Branches: $branchCount found",
                style = MaterialTheme.typography.bodyMedium
            )

            if (branches.isNotEmpty()) {
                Text(
                    text = "Branch names: ${branches.joinToString(", ")}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onTestConnection,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Text("Test Connection", style = MaterialTheme.typography.bodySmall)
                }

                Button(
                    onClick = onTestDataFetch,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("Fetch Data", style = MaterialTheme.typography.bodySmall)
                }

                Button(
                    onClick = onDiagnose,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary
                    )
                ) {
                    Text("Diagnose", style = MaterialTheme.typography.bodySmall)
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Check logcat for detailed debug information",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

// Mock product generation function removed - now using real Google Sheets data
