package rt.tt.temp.ui.viewmodels

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.preference.PreferenceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import rt.tt.temp.data.AppDatabase
import rt.tt.temp.services.GoogleSheetsApiService
import rt.tt.temp.services.DuplicateHandlingService
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

class GoogleSheetsViewModel(private val context: Context) : ViewModel() {
    val sheetsService = GoogleSheetsApiService() // Made public for AdminDashboard access
    val database = AppDatabase.getInstance(context) // Made public for AdminDashboard access
    private val prefs: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(context)
    private val tag = "GoogleSheetsViewModel"

    private val _isConnected = MutableStateFlow(prefs.getBoolean("google_sheets_is_connected", false))
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _lastSyncTime = MutableStateFlow<String?>(prefs.getString("google_sheets_last_sync_time", null))
    val lastSyncTime: StateFlow<String?> = _lastSyncTime.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _operationStatus = MutableStateFlow<String?>(null)
    val operationStatus: StateFlow<String?> = _operationStatus.asStateFlow()

    private val _productCount = MutableStateFlow(0)
    val productCount: StateFlow<Int> = _productCount.asStateFlow()

    private val _availableBranches = MutableStateFlow<List<String>>(emptyList())
    val availableBranches: StateFlow<List<String>> = _availableBranches.asStateFlow()

    private val _selectedBranch = MutableStateFlow<String?>(null)
    val selectedBranch: StateFlow<String?> = _selectedBranch.asStateFlow()

    private val _branchProductCounts = MutableStateFlow<Map<String, Int>>(emptyMap())
    val branchProductCounts: StateFlow<Map<String, Int>> = _branchProductCounts.asStateFlow()

    // Settings state flows
    private val _autoConnect = MutableStateFlow(prefs.getBoolean("google_sheets_auto_connect", false))
    val autoConnect: StateFlow<Boolean> = _autoConnect.asStateFlow()

    private val _autoSync = MutableStateFlow(prefs.getBoolean("google_sheets_auto_sync", false))
    val autoSync: StateFlow<Boolean> = _autoSync.asStateFlow()

    private val _syncInterval = MutableStateFlow(prefs.getString("google_sheets_sync_interval", "1 Day") ?: "1 Day")
    val syncInterval: StateFlow<String> = _syncInterval.asStateFlow()

    // Auto-sync state
    private val _isAutoSyncing = MutableStateFlow(false)
    val isAutoSyncing: StateFlow<Boolean> = _isAutoSyncing.asStateFlow()

    private val _pendingChanges = MutableStateFlow(false)
    val pendingChanges: StateFlow<Boolean> = _pendingChanges.asStateFlow()

    init {
        // Load initial data
        viewModelScope.launch {
            try {
                val products = database.productDao().getAllProducts()
                _productCount.value = products.size

                // Load available branches from local data
                loadLocalBranches()

                // Validate saved connection state
                validateConnectionState()

                // Auto-connect if enabled
                if (_autoConnect.value) {
                    Log.d(tag, "Auto-connect enabled, attempting connection...")
                    testConnection()
                }

                // Start monitoring for auto-sync
                startAutoSyncMonitoring()
            } catch (e: Exception) {
                Log.e(tag, "Failed to load initial data", e)
            }
        }
    }

    private suspend fun loadLocalBranches() {
        try {
            val products = database.productDao().getAllProducts()
            val branches = products.map { it.branchName }.distinct().sorted()
            _availableBranches.value = branches

            // Calculate product counts per branch
            val branchCounts = branches.associateWith { branch ->
                products.count { it.branchName == branch }
            }
            _branchProductCounts.value = branchCounts

            Log.d(tag, "Loaded ${branches.size} branches: $branches")
        } catch (e: Exception) {
            Log.e(tag, "Failed to load local branches", e)
        }
    }

    private suspend fun validateConnectionState() {
        try {
            // If SharedPreferences says we're connected, validate it
            if (_isConnected.value) {
                Log.d(tag, "Validating saved connection state...")
                val isActuallyConnected = sheetsService.testConnection()

                if (!isActuallyConnected) {
                    Log.w(tag, "Saved connection state is invalid, resetting...")
                    _isConnected.value = false
                    prefs.edit().putBoolean("google_sheets_is_connected", false).apply()
                    _lastSyncTime.value = null
                    prefs.edit().remove("google_sheets_last_sync_time").apply()
                    _operationStatus.value = "Connection lost. Please reconnect to Google Sheets."
                } else {
                    Log.d(tag, "Saved connection state is valid")
                    _operationStatus.value = "✅ Connected to Google Sheets"
                }
            } else {
                Log.d(tag, "No saved connection state found")
                _operationStatus.value = null
            }
        } catch (e: Exception) {
            Log.e(tag, "Failed to validate connection state", e)
            // Reset connection state on validation error
            _isConnected.value = false
            prefs.edit().putBoolean("google_sheets_is_connected", false).apply()
            _operationStatus.value = "Connection validation failed. Please reconnect."
        }
    }

    fun testConnection() {
        viewModelScope.launch {
            try {
                // Reset states first
                _isLoading.value = true
                _operationStatus.value = "Testing connection to Google Sheets..."
                _errorMessage.value = null

                // Clear any stale connection state
                _isConnected.value = false
                prefs.edit().putBoolean("google_sheets_is_connected", false).apply()

                Log.d(tag, "Starting fresh connection test")
                val success = sheetsService.testConnection()

                if (success) {
                    _isConnected.value = true
                    prefs.edit().putBoolean("google_sheets_is_connected", true).apply()
                    _operationStatus.value = "✅ Connected to Google Sheets successfully!"
                    Log.d(tag, "Connection test successful")
                } else {
                    _isConnected.value = false
                    prefs.edit().putBoolean("google_sheets_is_connected", false).apply()
                    _errorMessage.value = "❌ Failed to connect to Google Sheets. Please check your internet connection."
                    _operationStatus.value = null
                    Log.e(tag, "Connection test failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Connection test exception", e)
                _isConnected.value = false
                prefs.edit().putBoolean("google_sheets_is_connected", false).apply()
                _errorMessage.value = "Connection error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun exportToSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📤 Exporting inventory to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting export to sheets")
                val products = database.productDao().getAllProducts()
                Log.d(tag, "Found ${products.size} products to export")

                if (products.isEmpty()) {
                    _operationStatus.value = "⚠️ No products found to export"
                    return@launch
                }

                val success = sheetsService.syncAllProducts(products)

                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                    _operationStatus.value = "✅ Successfully exported ${products.size} products to Google Sheets!"
                    Log.d(tag, "Export successful: ${products.size} products")
                } else {
                    _errorMessage.value = "❌ Export failed. Please try again."
                    _operationStatus.value = null
                    Log.e(tag, "Export failed")
                }
            } catch (e: Exception) {
                Log.e(tag, "Export exception", e)
                _errorMessage.value = "Export error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun importFromSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📥 Importing data from Google Sheets..."
            _errorMessage.value = null

            try {
                // Validate connection first
                if (!_isConnected.value) {
                    _errorMessage.value = "❌ Not connected to Google Sheets. Please connect first."
                    _operationStatus.value = null
                    return@launch
                }

                Log.d(tag, "Starting import from sheets")
                _operationStatus.value = "📥 Fetching products from Google Sheets..."

                val products = sheetsService.getAllProducts()
                Log.d(tag, "Retrieved ${products.size} products from sheets")

                if (products.isNotEmpty()) {
                    _operationStatus.value = "📥 Processing ${products.size} products..."

                    // Validate products before importing
                    val validProducts = products.filter { product ->
                        product.productName.isNotBlank() &&
                        product.barcode.isNotBlank() &&
                        product.branchName.isNotBlank()
                    }

                    if (validProducts.size != products.size) {
                        Log.w(tag, "Filtered out ${products.size - validProducts.size} invalid products")
                    }

                    if (validProducts.isNotEmpty()) {
                        _operationStatus.value = "📥 Saving ${validProducts.size} products to database..."

                        // Clear existing data and insert new data
                        database.productDao().deleteAll()
                        database.productDao().insertAll(validProducts)

                        // Update product count and reload branches
                        _productCount.value = validProducts.size
                        loadLocalBranches()

                        val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                            .format(Date())
                        _lastSyncTime.value = currentTime
                        prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()

                        val message = if (validProducts.size == products.size) {
                            "✅ Successfully imported ${validProducts.size} products from Google Sheets!"
                        } else {
                            "✅ Imported ${validProducts.size} valid products (${products.size - validProducts.size} invalid products skipped)"
                        }
                        _operationStatus.value = message
                        Log.d(tag, "Import successful: ${validProducts.size} products")
                    } else {
                        _errorMessage.value = "❌ All ${products.size} products from Google Sheets are invalid (missing required fields)"
                        _operationStatus.value = null
                        Log.e(tag, "All products are invalid")
                    }
                } else {
                    _operationStatus.value = "⚠️ No products found in Google Sheets to import"
                    Log.w(tag, "No products found in sheets")
                }
            } catch (e: Exception) {
                Log.e(tag, "Import exception", e)
                _errorMessage.value = "Import failed: ${e.message ?: "Unknown error"}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Import data from Google Sheets for a specific branch
     */
    fun importFromSheetsForBranch(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📎 Importing $branchName data from Google Sheets..."
            _errorMessage.value = null

            try {
                // Validate connection first
                if (!_isConnected.value) {
                    _errorMessage.value = "❌ Not connected to Google Sheets. Please connect first."
                    _operationStatus.value = null
                    return@launch
                }

                Log.d(tag, "Starting branch import from sheets: $branchName")
                _operationStatus.value = "📎 Fetching $branchName products from Google Sheets..."

                // Get all products and filter by branch
                val allProducts = sheetsService.getAllProducts()
                val branchProducts = allProducts.filter { it.branchName.equals(branchName, ignoreCase = true) }

                Log.d(tag, "Retrieved ${branchProducts.size} products for branch $branchName (from ${allProducts.size} total)")

                if (branchProducts.isNotEmpty()) {
                    _operationStatus.value = "📎 Processing ${branchProducts.size} products for $branchName..."

                    // Validate products before importing
                    val validProducts = branchProducts.filter { product ->
                        product.productName.isNotBlank() &&
                                product.barcode.isNotBlank() &&
                                product.branchName.isNotBlank()
                    }

                    if (validProducts.size != branchProducts.size) {
                        Log.w(tag, "Filtered out ${branchProducts.size - validProducts.size} invalid products for branch $branchName")
                    }

                    if (validProducts.isNotEmpty()) {
                        _operationStatus.value = "📎 Saving ${validProducts.size} products for $branchName..."

                        // Delete existing products for this branch only
                        database.productDao().deleteProductsByBranch(branchName)

                        // Insert new products for this branch
                        database.productDao().insertAll(validProducts)

                        // Update product count and reload branches
                        val totalProducts = database.productDao().getAllProducts().size
                        _productCount.value = totalProducts
                        loadLocalBranches()

                        val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                            .format(Date())
                        _lastSyncTime.value = currentTime
                        prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()

                        val message = if (validProducts.size == branchProducts.size) {
                            "✅ Successfully imported ${validProducts.size} products for branch $branchName!"
                        } else {
                            "✅ Imported ${validProducts.size} valid products for $branchName (${branchProducts.size - validProducts.size} invalid products skipped)"
                        }
                        _operationStatus.value = message
                        Log.d(tag, "Branch import successful: ${validProducts.size} products for $branchName")
                    } else {
                        _errorMessage.value = "❌ All ${branchProducts.size} products for branch $branchName are invalid (missing required fields)"
                        _operationStatus.value = null
                        Log.e(tag, "All products are invalid for branch $branchName")
                    }
                } else {
                    _operationStatus.value = "⚠️ No products found for branch $branchName in Google Sheets"
                    Log.w(tag, "No products found for branch $branchName")
                }
            } catch (e: Exception) {
                Log.e(tag, "Branch import exception for $branchName", e)
                _errorMessage.value = "Import failed for $branchName: ${e.message ?: "Unknown error"}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Smart bidirectional sync with comprehensive duplicate handling
     */
    fun smartSync(
        strategy: DuplicateHandlingService.DuplicateStrategy = DuplicateHandlingService.DuplicateStrategy.PREFER_REMOTE
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🧠 Performing smart sync with duplicate handling..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting smart sync with strategy: $strategy")

                // Get local products
                val localProducts = database.productDao().getAllProducts()
                Log.d(tag, "Local products: ${localProducts.size}")

                // Perform smart sync with duplicate handling
                val (success, resultMessage) = sheetsService.syncWithDuplicateHandling(localProducts, strategy)

                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                    _operationStatus.value = resultMessage
                    Log.d(tag, "Smart sync successful")
                } else {
                    _errorMessage.value = resultMessage
                    _operationStatus.value = null
                    Log.e(tag, "Smart sync failed: $resultMessage")
                }
            } catch (e: Exception) {
                Log.e(tag, "Smart sync exception", e)
                _errorMessage.value = "Smart sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun syncBidirectional() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Performing bidirectional sync..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting bidirectional sync")

                // First export local data
                val localProducts = database.productDao().getAllProducts()
                val exportSuccess = sheetsService.syncAllProducts(localProducts)

                if (!exportSuccess) {
                    _errorMessage.value = "❌ Failed to export local data"
                    return@launch
                }

                // Then import any additional data from sheets
                val sheetProducts = sheetsService.getAllProducts()

                // For now, we'll just update the local count
                _productCount.value = localProducts.size

                val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                    .format(Date())
                _lastSyncTime.value = currentTime
                prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                _operationStatus.value = "✅ Bidirectional sync completed successfully!"
                Log.d(tag, "Bidirectional sync successful")

            } catch (e: Exception) {
                Log.e(tag, "Bidirectional sync exception", e)
                _errorMessage.value = "Sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _errorMessage.value = null
    }

    fun clearStatus() {
        _operationStatus.value = null
    }

    fun disconnect() {
        _isConnected.value = false
        prefs.edit().putBoolean("google_sheets_is_connected", false).apply()
        _lastSyncTime.value = null
        prefs.edit().remove("google_sheets_last_sync_time").apply()
        _selectedBranch.value = null
        _operationStatus.value = "Disconnected from Google Sheets"
    }

    // Branch-wise operations
    fun selectBranch(branchName: String?) {
        _selectedBranch.value = branchName
        Log.d(tag, "Selected branch: $branchName")
    }

    fun exportBranchToSheets(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Syncing $branchName with Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting bidirectional sync for branch: $branchName")
                val products = database.productDao().getAllProducts()
                val branchProducts = products.filter { it.branchName == branchName }

                Log.d(tag, "Found ${branchProducts.size} products for branch $branchName")

                if (branchProducts.isEmpty()) {
                    _operationStatus.value = "⚠️ No products found for branch $branchName"
                    return@launch
                }

                // Step 1: Export local data to sheets
                _operationStatus.value = "📤 Uploading $branchName data..."
                val exportSuccess = sheetsService.syncProductsByBranch(branchName, branchProducts)

                if (!exportSuccess) {
                    _errorMessage.value = "❌ Failed to upload $branchName data. Check logs for details."
                    _operationStatus.value = "Upload failed - check connection and try again"
                    Log.e(tag, "Branch export failed: $branchName")
                    Log.e(tag, "Possible causes: 1) Apps Script not deployed, 2) Network issue, 3) Data format error")
                    return@launch
                }

                // Step 2: Import back to ensure consistency (optional verification step)
                _operationStatus.value = "📥 Verifying $branchName data..."
                val sheetProducts = sheetsService.getProductsByBranch(branchName)

                val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                    .format(Date())
                _lastSyncTime.value = currentTime
                prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                _operationStatus.value = "✅ Successfully synced $branchName (${branchProducts.size} products)!"
                Log.d(tag, "Branch sync successful: $branchName - ${branchProducts.size} products")

            } catch (e: Exception) {
                Log.e(tag, "Branch sync exception", e)
                _errorMessage.value = "Sync error for $branchName: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun importBranchFromSheets(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📥 Importing $branchName data from Google Sheets..."
            _errorMessage.value = null

            try {
                // Validate connection first
                if (!_isConnected.value) {
                    _errorMessage.value = "❌ Not connected to Google Sheets. Please connect first."
                    _operationStatus.value = null
                    return@launch
                }

                Log.d(tag, "Starting import for branch: $branchName")
                _operationStatus.value = "📥 Fetching $branchName products from Google Sheets..."

                val branchProducts = sheetsService.getProductsByBranch(branchName)
                Log.d(tag, "Retrieved ${branchProducts.size} products for branch $branchName")

                if (branchProducts.isNotEmpty()) {
                    _operationStatus.value = "📥 Processing ${branchProducts.size} products for $branchName..."

                    // Validate products before importing
                    val validProducts = branchProducts.filter { product ->
                        product.productName.isNotBlank() &&
                        product.barcode.isNotBlank() &&
                        product.branchName == branchName // Ensure branch matches
                    }

                    if (validProducts.size != branchProducts.size) {
                        Log.w(tag, "Filtered out ${branchProducts.size - validProducts.size} invalid products for branch $branchName")
                    }

                    if (validProducts.isNotEmpty()) {
                        _operationStatus.value = "📥 Saving ${validProducts.size} products for $branchName..."

                        // Remove existing products for this branch
                        val existingProducts = database.productDao().getAllProducts()
                        val otherBranchProducts = existingProducts.filter { it.branchName != branchName }

                        // Clear all and insert other branches + new branch data
                        database.productDao().deleteAll()
                        database.productDao().insertAll(otherBranchProducts + validProducts)

                        // Update counts
                        loadLocalBranches()

                        val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                            .format(Date())
                        _lastSyncTime.value = currentTime
                        prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()

                        val message = if (validProducts.size == branchProducts.size) {
                            "✅ Successfully imported ${validProducts.size} products for $branchName!"
                        } else {
                            "✅ Imported ${validProducts.size} valid products for $branchName (${branchProducts.size - validProducts.size} invalid products skipped)"
                        }
                        _operationStatus.value = message
                        Log.d(tag, "Branch import successful: $branchName - ${validProducts.size} products")
                    } else {
                        _errorMessage.value = "❌ All ${branchProducts.size} products for $branchName are invalid (missing required fields)"
                        _operationStatus.value = null
                        Log.e(tag, "All products for branch $branchName are invalid")
                    }
                } else {
                    _operationStatus.value = "⚠️ No products found for branch $branchName in Google Sheets"
                    Log.w(tag, "No products found for branch: $branchName")
                }
            } catch (e: Exception) {
                Log.e(tag, "Branch import exception", e)
                _errorMessage.value = "Import failed for $branchName: ${e.message ?: "Unknown error"}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun syncAllBranchesToSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔄 Syncing all branches to Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting sync for all branches")
                val branches = _availableBranches.value
                var successCount = 0
                var totalProducts = 0

                for (branchName in branches) {
                    val products = database.productDao().getAllProducts()
                    val branchProducts = products.filter { it.branchName == branchName }

                    if (branchProducts.isNotEmpty()) {
                        val success = sheetsService.syncProductsByBranch(branchName, branchProducts)
                        if (success) {
                            successCount++
                            totalProducts += branchProducts.size
                            Log.d(tag, "Synced branch $branchName: ${branchProducts.size} products")
                        } else {
                            Log.e(tag, "Failed to sync branch: $branchName")
                        }
                    }
                }

                if (successCount == branches.size) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                    _operationStatus.value = "✅ Successfully synced all $successCount branches ($totalProducts products)!"
                    Log.d(tag, "All branches sync successful: $successCount branches, $totalProducts products")
                } else {
                    _errorMessage.value = "⚠️ Synced $successCount of ${branches.size} branches. Some failed."
                    _operationStatus.value = null
                    Log.w(tag, "Partial sync: $successCount of ${branches.size} branches")
                }
            } catch (e: Exception) {
                Log.e(tag, "All branches sync exception", e)
                _errorMessage.value = "Sync error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadBranchesFromSheets() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "📋 Loading branches from Google Sheets..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Loading branches from sheets")
                val sheetBranches = sheetsService.getAllBranches()

                if (sheetBranches.isNotEmpty()) {
                    // Merge with local branches
                    val localBranches = _availableBranches.value
                    val allBranches = (localBranches + sheetBranches).distinct().sorted()
                    _availableBranches.value = allBranches

                    _operationStatus.value = "✅ Loaded ${sheetBranches.size} branches from Google Sheets"
                    Log.d(tag, "Loaded branches from sheets: $sheetBranches")
                } else {
                    _operationStatus.value = "⚠️ No branches found in Google Sheets"
                    Log.w(tag, "No branches found in sheets")
                }
            } catch (e: Exception) {
                Log.e(tag, "Load branches exception", e)
                _errorMessage.value = "Error loading branches: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Debug test method
    fun testBranchSync(branchName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔍 Testing sync for $branchName..."
            _errorMessage.value = null

            try {
                Log.d(tag, "Starting test sync for branch: $branchName")
                val result = sheetsService.testBranchSync(branchName)

                _operationStatus.value = "🔍 Test result: $result"
                Log.d(tag, "Test sync result: $result")

            } catch (e: Exception) {
                Log.e(tag, "Test sync exception", e)
                _errorMessage.value = "Test error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Settings update functions
    fun updateAutoConnect(enabled: Boolean) {
        _autoConnect.value = enabled
        prefs.edit().putBoolean("google_sheets_auto_connect", enabled).apply()
        Log.d(tag, "Auto connect setting updated: $enabled")
    }

    fun updateAutoSync(enabled: Boolean) {
        _autoSync.value = enabled
        prefs.edit().putBoolean("google_sheets_auto_sync", enabled).apply()
        Log.d(tag, "Auto sync setting updated: $enabled")
    }

    fun updateSyncInterval(interval: String) {
        _syncInterval.value = interval
        prefs.edit().putString("google_sheets_sync_interval", interval).apply()
        Log.d(tag, "Sync interval setting updated: $interval")
    }

    // Reset all Google Sheets state (useful after app data is cleared)
    fun resetGoogleSheetsState() {
        viewModelScope.launch {
            try {
                Log.d(tag, "Resetting Google Sheets state...")

                // Reset all state variables
                _isConnected.value = false
                _isLoading.value = false
                _lastSyncTime.value = null
                _operationStatus.value = null
                _errorMessage.value = null
                _selectedBranch.value = null
                _isAutoSyncing.value = false
                _pendingChanges.value = false

                // Clear all SharedPreferences
                prefs.edit().apply {
                    remove("google_sheets_is_connected")
                    remove("google_sheets_last_sync_time")
                    remove("google_sheets_auto_connect")
                    remove("google_sheets_auto_sync")
                    remove("google_sheets_sync_interval")
                    apply()
                }

                // Reload settings with defaults
                _autoConnect.value = prefs.getBoolean("google_sheets_auto_connect", false)
                _autoSync.value = prefs.getBoolean("google_sheets_auto_sync", false)
                _syncInterval.value = prefs.getString("google_sheets_sync_interval", "1 Day") ?: "1 Day"

                Log.d(tag, "Google Sheets state reset complete")
            } catch (e: Exception) {
                Log.e(tag, "Failed to reset Google Sheets state", e)
            }
        }
    }

    // Diagnostic function to test import functionality
    fun diagnoseImportIssues() {
        viewModelScope.launch {
            _isLoading.value = true
            _operationStatus.value = "🔍 Running import diagnostics..."
            _errorMessage.value = null

            try {
                Log.d(tag, "=== IMPORT DIAGNOSTICS START ===")

                // Test 1: Connection status
                Log.d(tag, "Test 1: Connection Status")
                _operationStatus.value = "🔍 Testing connection..."
                val connectionTest = sheetsService.testConnection()
                Log.d(tag, "Connection test result: $connectionTest")

                if (!connectionTest) {
                    _errorMessage.value = "❌ Diagnostic failed: No connection to Google Sheets"
                    return@launch
                }

                // Test 2: Try to get all products
                Log.d(tag, "Test 2: Get All Products")
                _operationStatus.value = "🔍 Testing product retrieval..."
                val allProducts = sheetsService.getAllProducts()
                Log.d(tag, "Retrieved ${allProducts.size} products")

                // Test 3: Try to get branches
                Log.d(tag, "Test 3: Get All Branches")
                _operationStatus.value = "🔍 Testing branch retrieval..."
                val allBranches = sheetsService.getAllBranches()
                Log.d(tag, "Retrieved ${allBranches.size} branches: $allBranches")

                // Test 4: Test branch-specific import
                if (allBranches.isNotEmpty()) {
                    val testBranch = allBranches.first()
                    Log.d(tag, "Test 4: Get Products for Branch '$testBranch'")
                    _operationStatus.value = "🔍 Testing branch-specific retrieval..."
                    val branchProducts = sheetsService.getProductsByBranch(testBranch)
                    Log.d(tag, "Retrieved ${branchProducts.size} products for branch $testBranch")
                }

                // Summary
                val summary = buildString {
                    appendLine("🔍 Import Diagnostics Complete:")
                    appendLine("• Connection: ${if (connectionTest) "✅ OK" else "❌ Failed"}")
                    appendLine("• Total Products: ${allProducts.size}")
                    appendLine("• Available Branches: ${allBranches.size}")
                    if (allBranches.isNotEmpty()) {
                        appendLine("• Branches: ${allBranches.joinToString(", ")}")
                    }
                    if (allProducts.isNotEmpty()) {
                        appendLine("• Sample Product: ${allProducts.first().productName}")
                    }
                }

                _operationStatus.value = summary
                Log.d(tag, "=== IMPORT DIAGNOSTICS COMPLETE ===")

            } catch (e: Exception) {
                Log.e(tag, "Import diagnostics failed", e)
                _errorMessage.value = "Diagnostic error: ${e.message}"
                _operationStatus.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Auto-sync functionality
    private fun startAutoSyncMonitoring() {
        viewModelScope.launch {
            while (true) {
                delay(5000) // Check every 5 seconds

                if (_autoSync.value && _isConnected.value && _pendingChanges.value && !_isLoading.value) {
                    Log.d(tag, "Auto-sync triggered - pending changes detected")
                    performAutoSync()
                }
            }
        }
    }

    private suspend fun performAutoSync() {
        if (_isAutoSyncing.value) {
            Log.d(tag, "Auto-sync already in progress, skipping")
            return
        }

        try {
            _isAutoSyncing.value = true
            Log.d(tag, "Starting auto-sync...")

            val products = database.productDao().getAllProducts()
            if (products.isNotEmpty()) {
                val success = sheetsService.syncAllProducts(products)

                if (success) {
                    val currentTime = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date())
                    _lastSyncTime.value = currentTime
                    prefs.edit().putString("google_sheets_last_sync_time", currentTime).apply()
                    _pendingChanges.value = false
                    _operationStatus.value = "🔄 Auto-synced ${products.size} products"
                    Log.d(tag, "Auto-sync successful: ${products.size} products")
                } else {
                    Log.e(tag, "Auto-sync failed")
                }
            }
        } catch (e: Exception) {
            Log.e(tag, "Auto-sync exception", e)
        } finally {
            _isAutoSyncing.value = false
        }
    }

    // Call this method when data is modified
    fun markDataAsChanged() {
        if (_autoSync.value) {
            _pendingChanges.value = true
            Log.d(tag, "Data marked as changed - auto-sync will trigger")
        }
    }

    // Database operation wrappers that trigger auto-sync
    suspend fun insertProduct(product: rt.tt.temp.data.Product) {
        database.productDao().insertProduct(product)
        markDataAsChanged()
        Log.d(tag, "Product inserted: ${product.productName}")
    }

    suspend fun updateProduct(product: rt.tt.temp.data.Product) {
        database.productDao().updateProduct(product)
        markDataAsChanged()
        Log.d(tag, "Product updated: ${product.productName}")
    }

    suspend fun deleteProduct(product: rt.tt.temp.data.Product) {
        database.productDao().deleteProduct(product)
        markDataAsChanged()
        Log.d(tag, "Product deleted: ${product.productName}")
    }

    suspend fun updateProductQuantity(productId: Long, newQuantity: Int) {
        database.productDao().updateProductQuantity(productId, newQuantity)
        markDataAsChanged()
        Log.d(tag, "Product quantity updated: ID=$productId, Quantity=$newQuantity")
    }
}
