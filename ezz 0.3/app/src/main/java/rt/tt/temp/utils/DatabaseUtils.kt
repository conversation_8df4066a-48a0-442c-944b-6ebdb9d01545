package rt.tt.temp.utils

import rt.tt.temp.data.Product
import rt.tt.temp.managers.GoogleSheetsManager
import android.util.Log

/**
 * Utility functions for database operations with auto-sync support
 */
object DatabaseUtils {
    private const val TAG = "DatabaseUtils"
    
    /**
     * Insert a product with auto-sync
     */
    suspend fun insertProduct(product: Product) {
        try {
            GoogleSheetsManager.getInstance().insertProduct(product)
            Log.d(TAG, "Product inserted with auto-sync: ${product.productName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to insert product: ${product.productName}", e)
            throw e
        }
    }
    
    /**
     * Update a product with auto-sync
     */
    suspend fun updateProduct(product: Product) {
        try {
            GoogleSheetsManager.getInstance().updateProduct(product)
            Log.d(TAG, "Product updated with auto-sync: ${product.productName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update product: ${product.productName}", e)
            throw e
        }
    }
    
    /**
     * Delete a product with auto-sync
     */
    suspend fun deleteProduct(product: Product) {
        try {
            GoogleSheetsManager.getInstance().deleteProduct(product)
            Log.d(TAG, "Product deleted with auto-sync: ${product.productName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete product: ${product.productName}", e)
            throw e
        }
    }
    
    /**
     * Update product quantity with auto-sync
     */
    suspend fun updateProductQuantity(productId: Long, newQuantity: Int) {
        try {
            GoogleSheetsManager.getInstance().updateProductQuantity(productId, newQuantity)
            Log.d(TAG, "Product quantity updated with auto-sync: ID=$productId, Quantity=$newQuantity")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update product quantity: ID=$productId", e)
            throw e
        }
    }
    
    /**
     * Mark data as changed (for manual operations)
     */
    fun markDataAsChanged() {
        GoogleSheetsManager.getInstance().markDataAsChanged()
        Log.d(TAG, "Data marked as changed for auto-sync")
    }
}
