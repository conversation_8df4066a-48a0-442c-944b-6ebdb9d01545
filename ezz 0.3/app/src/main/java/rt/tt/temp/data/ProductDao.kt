package rt.tt.temp.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface ProductDao {
    @Query("SELECT * FROM products")
    suspend fun getAllProducts(): List<Product>

    @Query("SELECT * FROM products")
    fun getAllProductsSync(): List<Product>

    @Query("SELECT * FROM products WHERE isExpired = 0 AND expireDate > :currentDate ORDER BY expireDate ASC")
    fun getAllActiveProducts(currentDate: Long): Flow<List<Product>>

    @Query("""
        SELECT * FROM products
        WHERE isExpired = 1
        OR expireDate <= :currentDate
        ORDER BY expireDate DESC
    """)
    fun getAllExpiredProducts(currentDate: Long): Flow<List<Product>>

    @Query("SELECT COUNT(*) FROM products WHERE isExpired = 0 AND expireDate > :currentDate")
    suspend fun getTotalProductCount(currentDate: Long): Int

    @Query("SELECT * FROM products WHERE isExpired = 0 AND expireDate > :currentDate ORDER BY expireDate ASC LIMIT 10")
    fun getNearestExpiringProducts(currentDate: Long): Flow<List<Product>>

    @Query("SELECT * FROM products WHERE branchName = :branchName ORDER BY expireDate ASC")
    suspend fun getProductsByBranch(branchName: String): List<Product>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product)

    @Delete
    suspend fun deleteProduct(product: Product)

    @Update
    suspend fun updateProduct(product: Product)

    @Query("UPDATE products SET currentQuantity = :newQuantity WHERE id = :productId")
    suspend fun updateProductQuantity(productId: Long, newQuantity: Int)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(products: List<Product>)

    @Query("DELETE FROM products")
    suspend fun deleteAll()

    @Query("DELETE FROM products WHERE branchName = :branchName")
    suspend fun deleteProductsByBranch(branchName: String)

    @Query("UPDATE products SET max_quantity_threshold = :threshold WHERE id = :productId")
    suspend fun updateThreshold(productId: Int, threshold: Int)
}
