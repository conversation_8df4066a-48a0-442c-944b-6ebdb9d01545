package rt.tt.temp.ui.screens

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.rememberCoroutineScope
import android.speech.tts.TextToSpeech
import android.util.Log
import kotlinx.coroutines.launch

import rt.tt.temp.R
import rt.tt.temp.data.Product
import rt.tt.temp.ui.components.TranslatableText
import rt.tt.temp.ui.viewmodels.TranslationViewModel
import rt.tt.temp.utils.TextToSpeechHelper
import java.util.Date

@Composable
fun DashboardScreen(
    products: List<Product>,
    currentDate: Date,
    translationViewModel: TranslationViewModel
) {
    // States to control the visibility of product popups
    val showExpiringProductsDialog = remember { mutableStateOf(false) }
    val showNearExpiryProductsDialog = remember { mutableStateOf(false) }
    val showSufficientProductsDialog = remember { mutableStateOf(false) }
    val showAllProductsDialog = remember { mutableStateOf(false) }

    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Language-aware TTS helper (replaces basic TTS)
    val languageAwareTTS = remember { LanguageAwareTTSHelper(context) }

    // Add these lines to extract time values from currentDate
    val currentYear = remember { currentDate.year + 1900 } // Java Date year is years since 1900
    val currentMonth = remember { currentDate.month + 1 } // Java Date month is 0-based
    val currentDay = remember { currentDate.date }

    DisposableEffect(Unit) {
        onDispose {
            languageAwareTTS.shutdown()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {

        // Removed dashboard header text
        Spacer(modifier = Modifier.height(8.dp))

        // Summary Cards in a more responsive layout
        val expiringCount = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) <= 30 }.size
        val nearExpiryCount = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) in 31..90 }.size
        val inStockCount = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) > 90 }.size

        // Add a subtle background for the grid
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.5f))
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 200.dp, max = 350.dp)
            ) {
                // Add responsive grid for different screen sizes
                val configuration = LocalConfiguration.current
                val screenWidthDp = configuration.screenWidthDp
                val columnCount = when {
                    screenWidthDp < 600 -> 2 // Single column for small screens
                    screenWidthDp < 840 -> 3 // Two columns for medium screens
                    else -> 4 // Four columns for large screens
                }

                LazyVerticalGrid(
                    columns = GridCells.Adaptive(minSize = (screenWidthDp / (columnCount + 0.5)).dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 200.dp, max = if (screenWidthDp > 600) 400.dp else 350.dp)
                ) {
                    item {
                        // Make the red card clickable to show expiring products
                        SummaryCard(
                            title = stringResource(R.string.expiring_products),
                            value = expiringCount.toString(),
                            icon = Icons.Filled.Warning,
                            color = Color(0xFFE53935), // Vibrant red
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 120.dp, max = 160.dp)
                                .aspectRatio(1.2f),
                            onClick = { showExpiringProductsDialog.value = true }
                        )
                    }

                    item {
                        SummaryCard(
                            title = stringResource(R.string.near_expiry),
                            value = nearExpiryCount.toString(),
                            icon = Icons.Filled.Warning,
                            color = Color(0xFFF57C00), // Vibrant orange
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 120.dp, max = 160.dp)
                                .aspectRatio(1.2f),
                            onClick = { showNearExpiryProductsDialog.value = true }
                        )
                    }

                    item {
                        SummaryCard(
                            title = stringResource(R.string.in_stock),
                            value = inStockCount.toString(),
                            icon = Icons.Filled.Check,
                            color = Color(0xFF43A047), // Vibrant green
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 120.dp, max = 160.dp)
                                .aspectRatio(1.2f),
                            onClick = { showSufficientProductsDialog.value = true }
                        )
                    }

                    item {
                        SummaryCard(
                            title = stringResource(R.string.total_items),
                            value = products.size.toString(),
                            icon = Icons.Filled.Inventory,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 120.dp, max = 160.dp)
                                .aspectRatio(1.2f),
                            onClick = { showAllProductsDialog.value = true }
                        )
                    }
                }
            }
        }

        // Expiry Overview Chart with premium styling
        ElevatedCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp)
                .shadow(
                    elevation = 6.dp,
                    shape = RoundedCornerShape(12.dp),
                    spotColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.15f),
                    ambientColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.08f)
                ),
            elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
            ) {
                // Simple, clean header for Expiry Overview
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Filled.BarChart,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = stringResource(R.string.expiry_overview),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    Icon(
                        imageVector = Icons.Filled.PieChart,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                Divider(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                    thickness = 1.dp
                )
                Spacer(modifier = Modifier.height(16.dp))
                ExpiryOverviewChart(products = products)
            }
        }

        // Upcoming Expiries Section with enhanced styling
        ElevatedCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.elevatedCardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.upcoming_expiries),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Icon(
                        imageVector = Icons.Filled.Alarm,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))

                val upcomingExpiryProducts = remember(products, currentDate) {
                    products
                        .filter { product ->
                            val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
                            daysUntilExpiry >= 0 && !product.isExpired
                        }
                        .sortedBy { it.expireDate }
                        .take(5)
                }

                if (upcomingExpiryProducts.isEmpty()) {
                    Text(
                        text = stringResource(R.string.no_upcoming_expiries),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                } else {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        upcomingExpiryProducts.forEach { product ->
                            ElevatedCard(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp),
                                colors = CardDefaults.elevatedCardColors(
                                    containerColor = when {
                                        ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt() <= 30 -> Color(0xFFFFE0E0)
                                        ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt() <= 90 -> Color(0xFFFFF3E0)
                                        else -> Color(0xFFE8F5E9)
                                    },
                                    contentColor = MaterialTheme.colorScheme.onSurface
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .weight(1f)
                                            .padding(end = 8.dp)
                                    ) {
                                        TranslatableText(
                                            text = product.productName,
                                            translationViewModel = translationViewModel,
                                            style = MaterialTheme.typography.titleMedium,
                                            fontWeight = FontWeight.Bold,
                                            maxLines = 2,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Filled.Category,
                                                contentDescription = null,
                                                modifier = Modifier.size(16.dp),
                                                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = product.productType,
                                                style = MaterialTheme.typography.bodySmall,
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                        Spacer(modifier = Modifier.height(2.dp))
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Filled.QrCode,
                                                contentDescription = null,
                                                modifier = Modifier.size(16.dp),
                                                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = product.barcode,
                                                style = MaterialTheme.typography.bodySmall,
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }

                                    Column(
                                        horizontalAlignment = Alignment.End
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = "${product.currentQuantity}",
                                                style = MaterialTheme.typography.titleMedium,
                                                fontWeight = FontWeight.Bold,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = stringResource(R.string.units),
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }

                                        Spacer(modifier = Modifier.height(4.dp))

                                        val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
                                        val expiryColor = when {
                                            daysUntilExpiry <= 30 -> Color.Red
                                            daysUntilExpiry <= 90 -> Color(0xFFB7860B)
                                            else -> Color(0xFF2E7D32)
                                        }

                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = when {
                                                    daysUntilExpiry <= 30 -> Icons.Filled.PriorityHigh
                                                    daysUntilExpiry <= 90 -> Icons.Filled.Warning
                                                    else -> Icons.Filled.CheckCircle
                                                },
                                                contentDescription = null,
                                                tint = expiryColor,
                                                modifier = Modifier.size(16.dp)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = if (daysUntilExpiry <= 0)
                                                    stringResource(R.string.expired)
                                                else
                                                    stringResource(R.string.days_left, daysUntilExpiry),
                                                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                                                color = expiryColor
                                            )
                                        }

                                        Spacer(modifier = Modifier.height(8.dp))

                                        // Language-aware TTS Button
                                        IconButton(
                                            onClick = {
                                                scope.launch {
                                                    val displayedText = translationViewModel.getTranslatedProductName(product.productName)
                                                    val isTranslated = displayedText != product.productName
                                                    languageAwareTTS.speakWithLanguageDetection(
                                                        originalText = product.productName,
                                                        displayedText = displayedText,
                                                        isTranslated = isTranslated
                                                    )
                                                }
                                            },
                                            modifier = Modifier.size(24.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.VolumeUp,
                                                contentDescription = "Speak product name",
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(20.dp)
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Function to display product list dialog
    @Composable
    fun ShowProductListDialog(
        showDialog: MutableState<Boolean>,
        products: List<Product>,
        title: String,
        titleColor: Color,
        backgroundColor: Color
    ) {
        AlertDialog(
            onDismissRequest = { showDialog.value = false },
            title = {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = titleColor
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    if (products.isEmpty()) {
                        Text(text = "No products in this category.")
                    } else {
                        products.forEach { product ->
                            val daysUntilExpiry = daysUntilExpiry(product, currentDate.time)

                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = backgroundColor.copy(alpha = 0.2f)
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(8.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .weight(1f)
                                            .padding(end = 8.dp)
                                    ) {
                                        TranslatableText(
                                            text = product.productName,
                                            translationViewModel = translationViewModel,
                                            fontWeight = FontWeight.Bold,
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                        Text(
                                            text = "${product.productType} - ${product.barcode}",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    Column(
                                        horizontalAlignment = Alignment.End
                                    ) {
                                        Text(
                                            text = if (daysUntilExpiry <= 0) "EXPIRED" else "$daysUntilExpiry days left",
                                            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                                            color = titleColor
                                        )

                                        Spacer(modifier = Modifier.height(4.dp))

                                        // Language-aware TTS Button for dialog products
                                        IconButton(
                                            onClick = {
                                                scope.launch {
                                                    val displayedText = translationViewModel.getTranslatedProductName(product.productName)
                                                    val isTranslated = displayedText != product.productName
                                                    languageAwareTTS.speakWithLanguageDetection(
                                                        originalText = product.productName,
                                                        displayedText = displayedText,
                                                        isTranslated = isTranslated
                                                    )
                                                }
                                            },
                                            modifier = Modifier.size(20.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.VolumeUp,
                                                contentDescription = "Speak product name",
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(16.dp)
                                            )
                                        }
                                    }
                                }
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showDialog.value = false }
                ) {
                    Text("Close")
                }
            }
        )
    }

    // Show dialog with expiring products (red card)
    if (showExpiringProductsDialog.value) {
        val expiringProducts = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) <= 30 }
        ShowProductListDialog(
            showDialog = showExpiringProductsDialog,
            products = expiringProducts,
            title = stringResource(R.string.expiring_products),
            titleColor = Color(0xFFE53935),
            backgroundColor = Color(0xFFFFE0E0)
        )
    }

    // Show dialog with near expiry products (orange card)
    if (showNearExpiryProductsDialog.value) {
        val nearExpiryProducts = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) in 31..90 }
        ShowProductListDialog(
            showDialog = showNearExpiryProductsDialog,
            products = nearExpiryProducts,
            title = stringResource(R.string.near_expiry),
            titleColor = Color(0xFFF57C00),
            backgroundColor = Color(0xFFFFF3E0)
        )
    }

    // Show dialog with sufficient products (green card)
    if (showSufficientProductsDialog.value) {
        val sufficientProducts = products.filter { !it.isExpired && daysUntilExpiry(it, currentDate.time) > 90 }
        ShowProductListDialog(
            showDialog = showSufficientProductsDialog,
            products = sufficientProducts,
            title = stringResource(R.string.in_stock),
            titleColor = Color(0xFF43A047),
            backgroundColor = Color(0xFFE8F5E9)
        )
    }

    // Show dialog with all products (blue card)
    if (showAllProductsDialog.value) {
        ShowProductListDialog(
            showDialog = showAllProductsDialog,
            products = products,
            title = stringResource(R.string.total_items),
            titleColor = MaterialTheme.colorScheme.primary,
            backgroundColor = MaterialTheme.colorScheme.primaryContainer
        )
    }
}

@Composable
private fun SummaryCard(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit = {}
) {
    // Create a more responsive card layout that adapts to different screen densities
    val configuration = LocalConfiguration.current
    val fontScale = configuration.fontScale
    val density = LocalDensity.current

    // Calculate responsive dimensions based on screen density
    val iconSizeBase = 22.dp  // Slightly larger icon size
    val iconSizeDp = with(density) { iconSizeBase.toPx().dp }
    val iconSizeMultiplier = 1.2f  // Smaller multiplier for the container
    val titleFontSize = 12.sp.value * fontScale
    val valueFontSize = 24.sp.value * fontScale  // Adjusted value font size

    Box(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(16.dp),
                spotColor = color.copy(alpha = 0.4f),
                ambientColor = color.copy(alpha = 0.2f)
            )
            .clickable { onClick() }
    ) {
        // Card with gradient background
        Card(
            modifier = Modifier.fillMaxSize(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Transparent
            )
        ) {
            // Gradient background
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                color.copy(alpha = 0.2f),
                                color.copy(alpha = 0.08f)
                            ),
                            start = Offset(0f, 0f),
                            end = Offset(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY)
                        )
                    )
            ) {
                // Content with new layout: icon at top, value in center, title at bottom
                Column(
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    // Top section with icon - slightly larger with subtle background
                    Box(
                        modifier = Modifier
                            .padding(top = 6.dp)
                            .size(iconSizeBase * 1.3f)
                            .background(color.copy(alpha = 0.1f), RoundedCornerShape(6.dp)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = icon,
                            contentDescription = title,
                            tint = color,
                            modifier = Modifier.size(iconSizeBase)
                        )
                    }

                    // Center section with value - with background for visibility
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .background(Color.White.copy(alpha = 0.8f), RoundedCornerShape(6.dp))
                                .padding(horizontal = 8.dp, vertical = 4.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = value,
                                fontSize = valueFontSize.sp,
                                color = color,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                letterSpacing = 0.5.sp
                            )
                        }
                    }

                    // Bottom section with title - simplified with better contrast
                    Text(
                        text = title,
                        fontSize = titleFontSize.sp,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = FontWeight.Medium,
                        lineHeight = (titleFontSize * 1.2f).sp,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(color.copy(alpha = 0.1f), RoundedCornerShape(4.dp))
                            .padding(horizontal = 6.dp, vertical = 4.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExpiryCard(product: Product) {
    val currentDate = remember { Date() }
    val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()

    val containerColor = when {
        daysUntilExpiry <= 30 -> Color(0xFFFFE0E0)  // Light red - urgent
        daysUntilExpiry <= 90 -> Color(0xFFFFF3E0)  // Light yellow - soon
        else -> Color(0xFFE8F5E9)  // Light green - safe
    }

    val textColor = when {
        daysUntilExpiry <= 30 -> Color.Red  // Red - urgent
        daysUntilExpiry <= 90 -> Color(0xFFB7860B)  // Dark yellow - soon
        else -> Color(0xFF2E7D32)  // Dark green - safe
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = containerColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 8.dp)
            ) {
                Text(
                    text = product.productName,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = "${product.productType} - ${product.batch}",
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${product.currentQuantity} units",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = if (daysUntilExpiry <= 0) "EXPIRED" else "$daysUntilExpiry days left",
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                    color = textColor
                )
            }
        }
    }
}

@Composable
fun ExpiryOverviewChart(products: List<Product>) {
    val currentDate = Date()

    val lessThanMonth = products.count { product ->
        val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
        daysUntilExpiry in 0..30 && !product.isExpired
    }

    val oneToThreeMonths = products.count { product ->
        val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
        daysUntilExpiry in 31..90 && !product.isExpired
    }

    val moreThanThreeMonths = products.count { product ->
        val daysUntilExpiry = ((product.expireDate - currentDate.time) / (1000 * 60 * 60 * 24)).toInt()
        daysUntilExpiry > 90 && !product.isExpired
    }

    val total = products.size.toFloat().coerceAtLeast(1f)
    val data = listOf(
        Triple("< 1 month", (lessThanMonth / total) * 100, Color(0xFFFF6B6B)),
        Triple("1-3 months", (oneToThreeMonths / total) * 100, Color(0xFFFFB84D)),
        Triple("> 3 months", (moreThanThreeMonths / total) * 100, Color(0xFF4CAF50))
    )

    // Extract theme colors before using them in Canvas
    val surfaceColor = MaterialTheme.colorScheme.surface
    val surfaceVariantColor = MaterialTheme.colorScheme.surfaceVariant
    val primaryColor = MaterialTheme.colorScheme.primary
    val onSurfaceColor = MaterialTheme.colorScheme.onSurface

    // Add legend below the chart
    Column {
        Box(modifier = Modifier.weight(1f)) {
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .padding(horizontal = 8.dp, vertical = 16.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                surfaceColor.copy(alpha = 0.95f),
                                surfaceVariantColor.copy(alpha = 0.05f)
                            )
                        )
                    )
                    .border(
                        width = 0.5.dp,
                        color = primaryColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(8.dp)
            ) {
                val canvasWidth = size.width
                val canvasHeight = size.height
                val barWidth = (canvasWidth - 40f) / data.size
                val maxHeight = canvasHeight - 40f

                // Draw simple background grid
                drawRect(
                    color = surfaceColor.copy(alpha = 0.05f),
                    topLeft = Offset(40f, 0f),
                    size = Size(size.width - 40f, size.height - 40f)
                )

                // Draw Y-axis labels and lines with clean styling
                for (i in 0..5) {
                    val y = size.height - (i * maxHeight / 5)

                    // Draw horizontal grid lines
                    drawLine(
                        color = primaryColor.copy(alpha = 0.2f),
                        start = Offset(40f, y),
                        end = Offset(size.width, y),
                        strokeWidth = if (i == 0) 1f else 0.5f
                    )

                    // Draw percentage labels
                    drawIntoCanvas { canvas ->
                        canvas.nativeCanvas.drawText(
                            "${(i * 20)}%",
                            10f,
                            y + 5f,
                            android.graphics.Paint().apply {
                                color = android.graphics.Color.rgb(
                                    onSurfaceColor.red.toInt(),
                                    onSurfaceColor.green.toInt(),
                                    onSurfaceColor.blue.toInt()
                                )
                                alpha = 180
                                textSize = 24f
                                textAlign = android.graphics.Paint.Align.RIGHT
                                isFakeBoldText = i == 0 || i == 5  // Bold for 0% and 100%
                            }
                        )
                    }
                }

                // Draw bars
                data.forEachIndexed { index, (label, value, color) ->
                    val x = 40f + (index * barWidth) + (barWidth * 0.1f)
                    val barHeight = (value * maxHeight / 100)

                    // Draw simple bar
                    drawRect(
                        color = color,
                        topLeft = Offset(x, size.height - barHeight),
                        size = Size(barWidth * 0.8f, barHeight)
                    )

                    // Draw label
                    drawIntoCanvas { canvas ->
                        canvas.nativeCanvas.drawText(
                            label,
                            x + (barWidth * 0.4f),
                            size.height - 10f,
                            android.graphics.Paint().apply {
                                this.color = android.graphics.Color.rgb(
                                    onSurfaceColor.red.toInt(),
                                    onSurfaceColor.green.toInt(),
                                    onSurfaceColor.blue.toInt()
                                )
                                alpha = 200
                                textSize = 24f
                                textAlign = android.graphics.Paint.Align.CENTER
                            }
                        )
                    }
                }
            }
        }

        // Two-line legend with larger font
        Spacer(modifier = Modifier.height(8.dp))
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // First line: <1 month and 1-3 months
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                LegendItem(
                    label = data[0].first,
                    value = data[0].second,
                    color = data[0].third,
                    fontSize = 14.sp
                )
                Spacer(modifier = Modifier.width(24.dp))
                LegendItem(
                    label = data[1].first,
                    value = data[1].second,
                    color = data[1].third,
                    fontSize = 14.sp
                )
            }

            // Second line: >3 months
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                LegendItem(
                    label = data[2].first,
                    value = data[2].second,
                    color = data[2].third,
                    fontSize = 14.sp
                )
            }
        }
    }
}

// Add these helper functions to replace direct Date.time usage
fun daysUntilExpiry(product: Product, currentDate: Long): Int {
    return ((product.expireDate - currentDate) / (1000 * 60 * 60 * 24)).toInt()
}

// Overload for Date parameter
fun daysUntilExpiry(product: Product, currentDate: Date): Int {
    return daysUntilExpiry(product, currentDate.time)
}

@Composable
private fun LegendItem(
    label: String,
    value: Float,
    color: Color,
    fontSize: TextUnit = TextUnit.Unspecified
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        // Larger color indicator
        Box(
            modifier = Modifier
                .size(14.dp)
                .background(color, shape = RoundedCornerShape(3.dp))
        )
        Spacer(modifier = Modifier.width(6.dp))
        // Text with percentage and larger font
        Text(
            text = "$label (${value.toInt()}%)",
            style = MaterialTheme.typography.bodyMedium,
            fontSize = fontSize,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * Language-aware TTS helper that speaks in Arabic for untranslated text
 * and English for translated text
 */
class LanguageAwareTTSHelper(context: android.content.Context) {
    private var arabicTTS: TextToSpeech? = null
    private var englishTTS: TextToSpeech? = null
    private var isArabicReady = false
    private var isEnglishReady = false

    init {
        // Initialize Arabic TTS
        arabicTTS = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                val arabicLocale = java.util.Locale("ar")
                val result = arabicTTS?.setLanguage(arabicLocale)
                when (result) {
                    TextToSpeech.LANG_MISSING_DATA,
                    TextToSpeech.LANG_NOT_SUPPORTED -> {
                        Log.e("LanguageAwareTTS", "Arabic language not supported")
                    }
                    else -> {
                        isArabicReady = true
                        arabicTTS?.setPitch(1.0f)
                        arabicTTS?.setSpeechRate(0.8f)
                        Log.d("LanguageAwareTTS", "Arabic TTS initialized successfully")
                    }
                }
            } else {
                Log.e("LanguageAwareTTS", "Arabic TTS initialization failed")
            }
        }

        // Initialize English TTS
        englishTTS = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                val englishLocale = java.util.Locale.ENGLISH
                val result = englishTTS?.setLanguage(englishLocale)
                when (result) {
                    TextToSpeech.LANG_MISSING_DATA,
                    TextToSpeech.LANG_NOT_SUPPORTED -> {
                        Log.e("LanguageAwareTTS", "English language not supported")
                    }
                    else -> {
                        isEnglishReady = true
                        englishTTS?.setPitch(1.0f)
                        englishTTS?.setSpeechRate(0.9f)
                        Log.d("LanguageAwareTTS", "English TTS initialized successfully")
                    }
                }
            } else {
                Log.e("LanguageAwareTTS", "English TTS initialization failed")
            }
        }
    }

    /**
     * Speak text with automatic language detection based on translation status
     * @param originalText The original product name (usually Arabic)
     * @param displayedText The text currently displayed (translated or original)
     * @param isTranslated Whether the text has been translated
     */
    fun speakWithLanguageDetection(
        originalText: String,
        displayedText: String,
        isTranslated: Boolean
    ) {
        if (isTranslated) {
            // Text is translated to English, use English TTS
            if (isEnglishReady) {
                Log.d("LanguageAwareTTS", "Speaking in English: '$displayedText'")
                englishTTS?.speak(displayedText, TextToSpeech.QUEUE_FLUSH, null, null)
            } else {
                Log.w("LanguageAwareTTS", "English TTS not ready, falling back to Arabic")
                if (isArabicReady) {
                    arabicTTS?.speak(originalText, TextToSpeech.QUEUE_FLUSH, null, null)
                }
            }
        } else {
            // Text is not translated (original Arabic), use Arabic TTS
            if (isArabicReady) {
                Log.d("LanguageAwareTTS", "Speaking in Arabic: '$originalText'")
                arabicTTS?.speak(originalText, TextToSpeech.QUEUE_FLUSH, null, null)
            } else {
                Log.w("LanguageAwareTTS", "Arabic TTS not ready")
            }
        }
    }

    /**
     * Shutdown both TTS engines
     */
    fun shutdown() {
        arabicTTS?.stop()
        arabicTTS?.shutdown()
        englishTTS?.stop()
        englishTTS?.shutdown()
        Log.d("LanguageAwareTTS", "TTS engines shut down")
    }
}