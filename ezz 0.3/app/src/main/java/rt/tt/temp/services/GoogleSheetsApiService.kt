package rt.tt.temp.services

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import rt.tt.temp.data.Product
import rt.tt.temp.services.DuplicateHandlingService
import java.io.IOException

class GoogleSheetsApiService {
    private val client = OkHttpClient()
    private val gson = Gson()
    private val duplicateHandler = DuplicateHandlingService()

    // Your new clean Apps Script Web App URL
    private val scriptUrl = "https://script.google.com/macros/s/AKfycbz5oX7KbnfCKhnrS0kZFy7FtdurwmjebLxrdUf1Vw2F5mcVORp4l1NEhWas-yxIPhGU/exec"

    companion object {
        private const val TAG = "GoogleSheetsAPI"
    }

    data class ApiResponse(
        val success: Boolean,
        val message: String,
        val data: Any?,
        val timestamp: String
    )

    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Testing connection to: $scriptUrl")

            val requestBody = FormBody.Builder()
                .add("action", "test_connection")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Test connection response code: ${response.code}")
            Log.d(TAG, "Test connection response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                Log.d(TAG, "API Response success: ${apiResponse.success}")
                apiResponse.success
            } else {
                Log.e(TAG, "Response not successful or body is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Test connection failed", e)
            false
        }
    }

    suspend fun syncAllProducts(products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== SYNC ALL PRODUCTS WITH DUPLICATE HANDLING ===")
            Log.d(TAG, "Input products: ${products.size}")

            // Step 1: Clean and validate product data
            val cleanedProducts = duplicateHandler.cleanProductData(products)
            val validationIssues = duplicateHandler.validateProductData(cleanedProducts)

            if (validationIssues.isNotEmpty()) {
                Log.w(TAG, "Validation issues found:")
                validationIssues.forEach { issue ->
                    Log.w(TAG, "• $issue")
                }
            }

            // Step 2: Detect and resolve duplicates
            val duplicateResult = duplicateHandler.detectAndResolveDuplicates(
                cleanedProducts,
                DuplicateHandlingService.DuplicateStrategy.UPDATE_EXISTING
            )

            Log.d(TAG, "Duplicate detection completed:")
            Log.d(TAG, "• Original products: ${products.size}")
            Log.d(TAG, "• Unique products: ${duplicateResult.uniqueProducts.size}")
            Log.d(TAG, "• Duplicates resolved: ${duplicateResult.totalDuplicatesFound}")
            Log.d(TAG, duplicateResult.resolutionSummary)

            val finalProducts = duplicateResult.uniqueProducts
            Log.d(TAG, "Syncing ${finalProducts.size} unique products to Google Sheets")

            val productsJson = gson.toJson(finalProducts)
            Log.d(TAG, "Products JSON length: ${productsJson.length} characters")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products")
                .add("data", productsJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Sync response code: ${response.code}")
            Log.d(TAG, "Sync response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                Log.d(TAG, "Sync API Response success: ${apiResponse.success}")
                apiResponse.success
            } else {
                Log.e(TAG, "Sync response not successful or body is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Sync failed", e)
            false
        }
    }

    /**
     * Smart bidirectional sync with comprehensive duplicate handling
     */
    suspend fun syncWithDuplicateHandling(
        localProducts: List<Product>,
        strategy: DuplicateHandlingService.DuplicateStrategy = DuplicateHandlingService.DuplicateStrategy.PREFER_REMOTE
    ): Pair<Boolean, String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== SMART BIDIRECTIONAL SYNC ===")
            Log.d(TAG, "Local products: ${localProducts.size}")
            Log.d(TAG, "Strategy: $strategy")

            // Step 1: Get remote products
            val remoteProducts = getAllProducts()
            Log.d(TAG, "Remote products: ${remoteProducts.size}")

            // Step 2: Merge local and remote with duplicate handling
            val mergeResult = duplicateHandler.mergeLocalAndRemoteProducts(
                localProducts,
                remoteProducts,
                strategy
            )

            Log.d(TAG, "Merge completed:")
            Log.d(TAG, "• Final unique products: ${mergeResult.uniqueProducts.size}")
            Log.d(TAG, "• Conflicts resolved: ${mergeResult.totalDuplicatesFound}")
            Log.d(TAG, mergeResult.resolutionSummary)

            // Step 3: Sync the merged result back to Google Sheets
            val syncSuccess = syncAllProducts(mergeResult.uniqueProducts)

            val resultMessage = if (syncSuccess) {
                "✅ Smart sync completed successfully!\n" +
                "• Processed ${localProducts.size + remoteProducts.size} total products\n" +
                "• Resolved ${mergeResult.totalDuplicatesFound} conflicts\n" +
                "• Final result: ${mergeResult.uniqueProducts.size} unique products\n" +
                "• Strategy: ${strategy.name}\n\n" +
                mergeResult.resolutionSummary
            } else {
                "❌ Smart sync failed during upload phase"
            }

            return@withContext Pair(syncSuccess, resultMessage)

        } catch (e: Exception) {
            Log.e(TAG, "Smart sync failed", e)
            return@withContext Pair(false, "Smart sync error: ${e.message}")
        }
    }

    /**
     * Get products for a specific branch from Google Sheets
     */
    suspend fun getProductsByBranch(branchName: String): List<Product> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting products for branch: $branchName")

            val allProducts = getAllProducts()
            val branchProducts = allProducts.filter { it.branchName.equals(branchName, ignoreCase = true) }

            Log.d(TAG, "Found ${branchProducts.size} products for branch $branchName (from ${allProducts.size} total)")
            return@withContext branchProducts

        } catch (e: Exception) {
            Log.e(TAG, "Failed to get products for branch $branchName", e)
            throw e
        }
    }

    /**
     * Sync products for a specific branch to Google Sheets
     */
    suspend fun syncBranchProducts(branchName: String, products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== SYNC BRANCH PRODUCTS WITH DUPLICATE HANDLING ===")
            Log.d(TAG, "Branch: $branchName")
            Log.d(TAG, "Input products: ${products.size}")

            // Step 1: Clean and validate product data
            val cleanedProducts = duplicateHandler.cleanProductData(products)
            val validationIssues = duplicateHandler.validateProductData(cleanedProducts)

            if (validationIssues.isNotEmpty()) {
                Log.w(TAG, "Validation issues found for branch $branchName:")
                validationIssues.forEach { issue ->
                    Log.w(TAG, "• $issue")
                }
            }

            // Step 2: Detect and resolve duplicates within branch
            val duplicateResult = duplicateHandler.detectAndResolveDuplicates(
                cleanedProducts,
                DuplicateHandlingService.DuplicateStrategy.UPDATE_EXISTING
            )

            Log.d(TAG, "Duplicate detection completed for branch $branchName:")
            Log.d(TAG, "• Original products: ${products.size}")
            Log.d(TAG, "• Unique products: ${duplicateResult.uniqueProducts.size}")
            Log.d(TAG, "• Duplicates resolved: ${duplicateResult.totalDuplicatesFound}")
            Log.d(TAG, duplicateResult.resolutionSummary)

            // Step 3: Get all existing products from Google Sheets
            val allExistingProducts = getAllProducts()

            // Step 4: Remove existing products for this branch
            val otherBranchProducts = allExistingProducts.filter {
                !it.branchName.equals(branchName, ignoreCase = true)
            }

            // Step 5: Combine other branches with new branch products
            val finalProducts = otherBranchProducts + duplicateResult.uniqueProducts

            Log.d(TAG, "Final product count for sync: ${finalProducts.size}")
            Log.d(TAG, "• Other branches: ${otherBranchProducts.size}")
            Log.d(TAG, "• $branchName branch: ${duplicateResult.uniqueProducts.size}")

            // Step 6: Sync all products back to Google Sheets
            val syncSuccess = syncAllProducts(finalProducts)

            if (syncSuccess) {
                Log.d(TAG, "Branch sync successful for $branchName")
            } else {
                Log.e(TAG, "Branch sync failed for $branchName")
            }

            return@withContext syncSuccess

        } catch (e: Exception) {
            Log.e(TAG, "Branch sync failed for $branchName", e)
            return@withContext false
        }
    }

    /**
     * Sync products for a specific branch to its own sheet (e.g., "Baqaa Products")
     */
    suspend fun syncBranchProductsToSheet(branchName: String, products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== SYNC BRANCH TO SPECIFIC SHEET ===")
            Log.d(TAG, "Branch: $branchName")
            Log.d(TAG, "Products: ${products.size}")
            Log.d(TAG, "Target sheet: ${branchName} Products")

            // Clean and validate product data
            val cleanedProducts = duplicateHandler.cleanProductData(products)
            val validationIssues = duplicateHandler.validateProductData(cleanedProducts)

            if (validationIssues.isNotEmpty()) {
                Log.w(TAG, "Validation issues found for branch $branchName:")
                validationIssues.forEach { issue ->
                    Log.w(TAG, "• $issue")
                }
            }

            // Detect and resolve duplicates within branch
            val duplicateResult = duplicateHandler.detectAndResolveDuplicates(
                cleanedProducts,
                DuplicateHandlingService.DuplicateStrategy.UPDATE_EXISTING
            )

            Log.d(TAG, "Duplicate detection completed for branch $branchName:")
            Log.d(TAG, "• Original products: ${products.size}")
            Log.d(TAG, "• Unique products: ${duplicateResult.uniqueProducts.size}")
            Log.d(TAG, "• Duplicates resolved: ${duplicateResult.totalDuplicatesFound}")

            // Prepare data for Google Apps Script
            val requestData = mapOf(
                "branchName" to branchName,
                "products" to duplicateResult.uniqueProducts
            )

            val requestBody = FormBody.Builder()
                .add("action", "sync_products_by_branch")
                .add("data", gson.toJson(requestData))
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending branch sync request to Google Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== BRANCH SYNC RESPONSE ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    if (apiResponse.success) {
                        Log.d(TAG, "Branch sync successful for $branchName: ${apiResponse.message}")
                        return@withContext true
                    } else {
                        Log.e(TAG, "Branch sync failed for $branchName: ${apiResponse.message}")
                    }
                } catch (jsonE: Exception) {
                    Log.e(TAG, "Failed to parse branch sync response for $branchName", jsonE)
                }
            } else {
                Log.e(TAG, "HTTP request failed for branch sync $branchName - Code: ${response.code}")
            }

            return@withContext false

        } catch (e: Exception) {
            Log.e(TAG, "Branch sync to sheet failed for $branchName", e)
            return@withContext false
        }
    }

    suspend fun getAllProducts(): List<Product> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== IMPORT ALL PRODUCTS DEBUG ===")
            Log.d(TAG, "Getting all products from Google Sheets")
            Log.d(TAG, "Script URL: $scriptUrl")

            val requestBody = FormBody.Builder()
                .add("action", "get_products")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending request to Google Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== IMPORT RESPONSE DEBUG ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response message: ${response.message}")
            Log.d(TAG, "Response headers: ${response.headers}")
            Log.d(TAG, "Response body length: ${responseBody?.length ?: 0}")
            Log.d(TAG, "Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    Log.d(TAG, "API Response success: ${apiResponse.success}")
                    Log.d(TAG, "API Response message: ${apiResponse.message}")
                    Log.d(TAG, "API Response data type: ${apiResponse.data?.javaClass?.simpleName}")

                    if (apiResponse.success && apiResponse.data != null) {
                        val type = object : TypeToken<List<Product>>() {}.type
                        val dataJson = gson.toJson(apiResponse.data)
                        Log.d(TAG, "Data JSON length: ${dataJson.length}")

                        val products: List<Product> = gson.fromJson(dataJson, type)
                        Log.d(TAG, "Successfully parsed ${products.size} products from sheets")

                        // Log sample products for debugging
                        products.take(3).forEachIndexed { index, product ->
                            Log.d(TAG, "Sample product $index: ${product.productName} - ${product.branchName} - ${product.barcode}")
                        }

                        return@withContext products
                    } else {
                        Log.w(TAG, "API response indicates failure or no data: ${apiResponse.message}")
                    }
                } catch (jsonE: Exception) {
                    Log.e(TAG, "Failed to parse JSON response", jsonE)
                    Log.e(TAG, "Raw response that failed to parse: $responseBody")
                }
            } else {
                Log.w(TAG, "HTTP request failed - Code: ${response.code}, Message: ${response.message}")
            }

            Log.w(TAG, "No products retrieved - returning empty list")
            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Get products failed with exception", e)
            emptyList()
        }
    }

    suspend fun addProduct(product: Product): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding single product to Google Sheets")

            val productJson = gson.toJson(product)

            val requestBody = FormBody.Builder()
                .add("action", "add_product")
                .add("data", productJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Add product response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                apiResponse.success
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Add product failed", e)
            false
        }
    }

    // Branch-wise operations
    suspend fun syncProductsByBranch(branchName: String, products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== BRANCH SYNC DEBUG ===")
            Log.d(TAG, "Branch: $branchName")
            Log.d(TAG, "Products count: ${products.size}")
            Log.d(TAG, "Script URL: $scriptUrl")

            // Log first product for debugging
            if (products.isNotEmpty()) {
                val firstProduct = products[0]
                Log.d(TAG, "Sample product: ${firstProduct.productName} - ${firstProduct.branchName}")
            }

            val requestData = mapOf(
                "branchName" to branchName,
                "products" to products
            )
            val dataJson = gson.toJson(requestData)
            Log.d(TAG, "Request data size: ${dataJson.length} characters")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products_by_branch")
                .add("data", dataJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending request to Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== RESPONSE DEBUG ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response message: ${response.message}")
            Log.d(TAG, "Response body: $responseBody")
            Log.d(TAG, "Response headers: ${response.headers}")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    Log.d(TAG, "API Response parsed successfully")
                    Log.d(TAG, "API Success: ${apiResponse.success}")
                    Log.d(TAG, "API Message: ${apiResponse.message}")
                    Log.d(TAG, "API Data: ${apiResponse.data}")

                    if (!apiResponse.success) {
                        Log.e(TAG, "Apps Script returned success=false: ${apiResponse.message}")
                    }

                    return@withContext apiResponse.success
                } catch (jsonException: Exception) {
                    Log.e(TAG, "Failed to parse JSON response", jsonException)
                    Log.e(TAG, "Raw response was: $responseBody")
                    return@withContext false
                }
            } else {
                Log.e(TAG, "HTTP request failed")
                Log.e(TAG, "Response code: ${response.code}")
                Log.e(TAG, "Response message: ${response.message}")
                Log.e(TAG, "Response body: $responseBody")
                return@withContext false
            }
        } catch (e: Exception) {
            Log.e(TAG, "=== EXCEPTION DEBUG ===")
            Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "Exception message: ${e.message}")
            Log.e(TAG, "Exception stack trace:", e)
            return@withContext false
        }
    }



    suspend fun getAllBranches(): List<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting all branches")

            val requestBody = FormBody.Builder()
                .add("action", "get_all_branches")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Get branches response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                if (apiResponse.success && apiResponse.data != null) {
                    val type = object : TypeToken<List<String>>() {}.type
                    val dataJson = gson.toJson(apiResponse.data)
                    val branches: List<String> = gson.fromJson(dataJson, type)
                    Log.d(TAG, "Retrieved ${branches.size} branches")
                    return@withContext branches
                }
            }

            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Get branches failed", e)
            emptyList()
        }
    }

    suspend fun syncAllBranches(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Syncing all branches")

            val requestBody = FormBody.Builder()
                .add("action", "sync_all_branches")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Sync all branches response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                apiResponse.success
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Sync all branches failed", e)
            false
        }
    }

    // Simple test method for debugging
    suspend fun testBranchSync(branchName: String): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== TESTING BRANCH SYNC ===")
            Log.d(TAG, "Testing branch: $branchName")

            // Create minimal test data
            val testProduct = mapOf(
                "id" to 999,
                "productName" to "Test Product",
                "barcode" to "TEST123",
                "productType" to "Test",
                "branchName" to branchName,
                "currentQuantity" to 1,
                "initialQuantity" to 1,
                "mfgDate" to System.currentTimeMillis(),
                "expireDate" to System.currentTimeMillis() + 86400000, // +1 day
                "isExpired" to false,
                "batch" to "TEST"
            )

            val requestData = mapOf(
                "branchName" to branchName,
                "products" to listOf(testProduct)
            )

            val dataJson = gson.toJson(requestData)
            Log.d(TAG, "Test data JSON: $dataJson")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products_by_branch")
                .add("data", dataJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: "No response body"

            Log.d(TAG, "Test response code: ${response.code}")
            Log.d(TAG, "Test response: $responseBody")

            return@withContext "Code: ${response.code}, Body: $responseBody"

        } catch (e: Exception) {
            Log.e(TAG, "Test failed", e)
            return@withContext "Error: ${e.message}"
        }
    }
}
