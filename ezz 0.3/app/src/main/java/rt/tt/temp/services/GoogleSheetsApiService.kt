package rt.tt.temp.services

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import rt.tt.temp.data.Product
import java.io.IOException

class GoogleSheetsApiService {
    private val client = OkHttpClient()
    private val gson = Gson()

    // Your new clean Apps Script Web App URL
    private val scriptUrl = "https://script.google.com/macros/s/AKfycbz5oX7KbnfCKhnrS0kZFy7FtdurwmjebLxrdUf1Vw2F5mcVORp4l1NEhWas-yxIPhGU/exec"

    companion object {
        private const val TAG = "GoogleSheetsAPI"
    }

    data class ApiResponse(
        val success: Boolean,
        val message: String,
        val data: Any?,
        val timestamp: String
    )

    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Testing connection to: $scriptUrl")

            val requestBody = FormBody.Builder()
                .add("action", "test_connection")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Test connection response code: ${response.code}")
            Log.d(TAG, "Test connection response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                Log.d(TAG, "API Response success: ${apiResponse.success}")
                apiResponse.success
            } else {
                Log.e(TAG, "Response not successful or body is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Test connection failed", e)
            false
        }
    }

    suspend fun syncAllProducts(products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Syncing ${products.size} products to Google Sheets")

            val productsJson = gson.toJson(products)
            Log.d(TAG, "Products JSON: $productsJson")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products")
                .add("data", productsJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Sync response code: ${response.code}")
            Log.d(TAG, "Sync response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                Log.d(TAG, "Sync API Response success: ${apiResponse.success}")
                apiResponse.success
            } else {
                Log.e(TAG, "Sync response not successful or body is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Sync failed", e)
            false
        }
    }

    suspend fun getAllProducts(): List<Product> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== IMPORT ALL PRODUCTS DEBUG ===")
            Log.d(TAG, "Getting all products from Google Sheets")
            Log.d(TAG, "Script URL: $scriptUrl")

            val requestBody = FormBody.Builder()
                .add("action", "get_products")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending request to Google Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== IMPORT RESPONSE DEBUG ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response message: ${response.message}")
            Log.d(TAG, "Response headers: ${response.headers}")
            Log.d(TAG, "Response body length: ${responseBody?.length ?: 0}")
            Log.d(TAG, "Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    Log.d(TAG, "API Response success: ${apiResponse.success}")
                    Log.d(TAG, "API Response message: ${apiResponse.message}")
                    Log.d(TAG, "API Response data type: ${apiResponse.data?.javaClass?.simpleName}")

                    if (apiResponse.success && apiResponse.data != null) {
                        val type = object : TypeToken<List<Product>>() {}.type
                        val dataJson = gson.toJson(apiResponse.data)
                        Log.d(TAG, "Data JSON length: ${dataJson.length}")

                        val products: List<Product> = gson.fromJson(dataJson, type)
                        Log.d(TAG, "Successfully parsed ${products.size} products from sheets")

                        // Log sample products for debugging
                        products.take(3).forEachIndexed { index, product ->
                            Log.d(TAG, "Sample product $index: ${product.productName} - ${product.branchName} - ${product.barcode}")
                        }

                        return@withContext products
                    } else {
                        Log.w(TAG, "API response indicates failure or no data: ${apiResponse.message}")
                    }
                } catch (jsonE: Exception) {
                    Log.e(TAG, "Failed to parse JSON response", jsonE)
                    Log.e(TAG, "Raw response that failed to parse: $responseBody")
                }
            } else {
                Log.w(TAG, "HTTP request failed - Code: ${response.code}, Message: ${response.message}")
            }

            Log.w(TAG, "No products retrieved - returning empty list")
            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Get products failed with exception", e)
            emptyList()
        }
    }

    suspend fun addProduct(product: Product): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Adding single product to Google Sheets")

            val productJson = gson.toJson(product)

            val requestBody = FormBody.Builder()
                .add("action", "add_product")
                .add("data", productJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Add product response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                apiResponse.success
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Add product failed", e)
            false
        }
    }

    // Branch-wise operations
    suspend fun syncProductsByBranch(branchName: String, products: List<Product>): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== BRANCH SYNC DEBUG ===")
            Log.d(TAG, "Branch: $branchName")
            Log.d(TAG, "Products count: ${products.size}")
            Log.d(TAG, "Script URL: $scriptUrl")

            // Log first product for debugging
            if (products.isNotEmpty()) {
                val firstProduct = products[0]
                Log.d(TAG, "Sample product: ${firstProduct.productName} - ${firstProduct.branchName}")
            }

            val requestData = mapOf(
                "branchName" to branchName,
                "products" to products
            )
            val dataJson = gson.toJson(requestData)
            Log.d(TAG, "Request data size: ${dataJson.length} characters")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products_by_branch")
                .add("data", dataJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending request to Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== RESPONSE DEBUG ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response message: ${response.message}")
            Log.d(TAG, "Response body: $responseBody")
            Log.d(TAG, "Response headers: ${response.headers}")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    Log.d(TAG, "API Response parsed successfully")
                    Log.d(TAG, "API Success: ${apiResponse.success}")
                    Log.d(TAG, "API Message: ${apiResponse.message}")
                    Log.d(TAG, "API Data: ${apiResponse.data}")

                    if (!apiResponse.success) {
                        Log.e(TAG, "Apps Script returned success=false: ${apiResponse.message}")
                    }

                    return@withContext apiResponse.success
                } catch (jsonException: Exception) {
                    Log.e(TAG, "Failed to parse JSON response", jsonException)
                    Log.e(TAG, "Raw response was: $responseBody")
                    return@withContext false
                }
            } else {
                Log.e(TAG, "HTTP request failed")
                Log.e(TAG, "Response code: ${response.code}")
                Log.e(TAG, "Response message: ${response.message}")
                Log.e(TAG, "Response body: $responseBody")
                return@withContext false
            }
        } catch (e: Exception) {
            Log.e(TAG, "=== EXCEPTION DEBUG ===")
            Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "Exception message: ${e.message}")
            Log.e(TAG, "Exception stack trace:", e)
            return@withContext false
        }
    }

    suspend fun getProductsByBranch(branchName: String): List<Product> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== IMPORT BRANCH PRODUCTS DEBUG ===")
            Log.d(TAG, "Getting products for branch: $branchName")
            Log.d(TAG, "Script URL: $scriptUrl")

            val requestBody = FormBody.Builder()
                .add("action", "get_products_by_branch")
                .add("branchName", branchName)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            Log.d(TAG, "Sending branch request to Google Apps Script...")
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "=== BRANCH IMPORT RESPONSE DEBUG ===")
            Log.d(TAG, "Response code: ${response.code}")
            Log.d(TAG, "Response message: ${response.message}")
            Log.d(TAG, "Response body length: ${responseBody?.length ?: 0}")
            Log.d(TAG, "Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                    Log.d(TAG, "API Response success: ${apiResponse.success}")
                    Log.d(TAG, "API Response message: ${apiResponse.message}")

                    if (apiResponse.success && apiResponse.data != null) {
                        val type = object : TypeToken<List<Product>>() {}.type
                        val dataJson = gson.toJson(apiResponse.data)
                        val products: List<Product> = gson.fromJson(dataJson, type)
                        Log.d(TAG, "Successfully parsed ${products.size} products for branch $branchName")

                        // Log sample products for debugging
                        products.take(3).forEachIndexed { index, product ->
                            Log.d(TAG, "Sample product $index: ${product.productName} - ${product.branchName} - ${product.barcode}")
                        }

                        return@withContext products
                    } else {
                        Log.w(TAG, "API response indicates failure or no data for branch $branchName: ${apiResponse.message}")
                    }
                } catch (jsonE: Exception) {
                    Log.e(TAG, "Failed to parse JSON response for branch $branchName", jsonE)
                    Log.e(TAG, "Raw response that failed to parse: $responseBody")
                }
            } else {
                Log.w(TAG, "HTTP request failed for branch $branchName - Code: ${response.code}, Message: ${response.message}")
            }

            Log.w(TAG, "No products retrieved for branch $branchName - returning empty list")
            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Get branch products failed for $branchName with exception", e)
            emptyList()
        }
    }

    suspend fun getAllBranches(): List<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Getting all branches")

            val requestBody = FormBody.Builder()
                .add("action", "get_all_branches")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Get branches response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                if (apiResponse.success && apiResponse.data != null) {
                    val type = object : TypeToken<List<String>>() {}.type
                    val dataJson = gson.toJson(apiResponse.data)
                    val branches: List<String> = gson.fromJson(dataJson, type)
                    Log.d(TAG, "Retrieved ${branches.size} branches")
                    return@withContext branches
                }
            }

            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Get branches failed", e)
            emptyList()
        }
    }

    suspend fun syncAllBranches(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Syncing all branches")

            val requestBody = FormBody.Builder()
                .add("action", "sync_all_branches")
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "Sync all branches response: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                val apiResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                apiResponse.success
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Sync all branches failed", e)
            false
        }
    }

    // Simple test method for debugging
    suspend fun testBranchSync(branchName: String): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== TESTING BRANCH SYNC ===")
            Log.d(TAG, "Testing branch: $branchName")

            // Create minimal test data
            val testProduct = mapOf(
                "id" to 999,
                "productName" to "Test Product",
                "barcode" to "TEST123",
                "productType" to "Test",
                "branchName" to branchName,
                "currentQuantity" to 1,
                "initialQuantity" to 1,
                "mfgDate" to System.currentTimeMillis(),
                "expireDate" to System.currentTimeMillis() + 86400000, // +1 day
                "isExpired" to false,
                "batch" to "TEST"
            )

            val requestData = mapOf(
                "branchName" to branchName,
                "products" to listOf(testProduct)
            )

            val dataJson = gson.toJson(requestData)
            Log.d(TAG, "Test data JSON: $dataJson")

            val requestBody = FormBody.Builder()
                .add("action", "sync_products_by_branch")
                .add("data", dataJson)
                .build()

            val request = Request.Builder()
                .url(scriptUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: "No response body"

            Log.d(TAG, "Test response code: ${response.code}")
            Log.d(TAG, "Test response: $responseBody")

            return@withContext "Code: ${response.code}, Body: $responseBody"

        } catch (e: Exception) {
            Log.e(TAG, "Test failed", e)
            return@withContext "Error: ${e.message}"
        }
    }
}
